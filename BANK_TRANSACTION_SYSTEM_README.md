# Bank Transaction and Real-time Email Monitoring System

This system provides comprehensive bank transaction recording and real-time Gmail email monitoring for banking balance changes in the GoldApp system.

## Features

### ✅ **Implemented Features**

1. **Bank Transaction Recording**
   - Complete `BankTransactionEntity` with all necessary fields
   - Transaction status tracking (pending, processed, failed)
   - Support for both credit and debit transactions
   - Automatic transaction ID generation and duplicate prevention

2. **Real-time Gmail Email Monitoring**
   - Gmail API integration for real-time email monitoring
   - Configurable email patterns for different banks
   - Automatic email parsing and transaction extraction
   - Scheduled monitoring every 5 minutes

3. **Payment Tracking Integration**
   - Automatic matching of bank transactions with payment tracking records
   - Status updates when payments are successfully received
   - Manual transaction matching for admin users

4. **Email Notifications**
   - Transaction detection notifications
   - Payment success notifications
   - Balance change alerts
   - Daily transaction summaries

5. **REST API Endpoints**
   - Complete CRUD operations for bank transactions
   - Monitoring status and statistics
   - Manual transaction management

6. **Database Schema**
   - Extended `bank_config` table with email monitoring fields
   - New `bank_transaction` table with proper relationships
   - Database migration scripts included

## Architecture

### **Core Components**

1. **Entities**
   - `BankTransactionEntity` - Records actual bank transactions
   - `BankConfigEntity` - Extended with email monitoring configuration
   - `PaymentTrackingEntity` - Existing payment tracking (enhanced)

2. **Services**
   - `GmailMonitoringService` - Gmail API integration
   - `BankTransactionParserService` - Email content parsing
   - `BankTransactionService` - Transaction business logic
   - `EmailNotificationService` - Email notifications
   - `EmailMonitoringScheduler` - Scheduled monitoring tasks

3. **Repositories**
   - `BankTransactionRepository` - Transaction data access
   - `BankConfigRepository` - Bank configuration access

4. **Controllers**
   - `BankTransactionController` - REST API endpoints

## Setup Instructions

### **1. Database Setup**

Run the database migration:
```sql
-- The migration script is located at:
-- src/main/resources/db/changelog/********-bank-transaction-system.sql
```

### **2. Gmail API Setup**

Follow the detailed setup guide in `GMAIL_SETUP_GUIDE.md`:

1. Create Google Cloud Project
2. Enable Gmail API
3. Create OAuth credentials
4. Download `credentials.json`
5. Configure application properties

### **3. Environment Variables**

Set the following environment variables:

```bash
# Email Configuration
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password

# Admin Notifications
ADMIN_EMAIL=<EMAIL>
FROM_EMAIL=<EMAIL>

# Gmail API
GMAIL_CREDENTIALS_PATH=credentials.json
GMAIL_TOKENS_DIR=tokens
```

### **4. Bank Configuration**

Configure your bank accounts in the database:

```sql
UPDATE bank_config SET 
    monitoring_email = '<EMAIL>',
    email_monitoring_enabled = 1,
    bank_email_sender = '<EMAIL>',
    transaction_email_subject_pattern = 'Transaction notification'
WHERE id = 1;
```

## API Endpoints

### **Transaction Management**

- `GET /api/bank-transactions/recent?hours=24` - Get recent transactions
- `GET /api/bank-transactions/bank/{bankId}` - Get transactions for specific bank
- `GET /api/bank-transactions/bank/{bankId}/unprocessed` - Get unprocessed transactions
- `PUT /api/bank-transactions/{transactionId}/status` - Update transaction status
- `POST /api/bank-transactions/{transactionId}/match/{paymentTrackingCode}` - Manual matching

### **Monitoring & Statistics**

- `GET /api/bank-transactions/statistics` - Get transaction statistics
- `GET /api/bank-transactions/monitoring/status` - Check monitoring status
- `POST /api/bank-transactions/monitor/trigger` - Trigger manual monitoring

### **Admin Functions**

- `POST /api/bank-transactions/manual` - Create manual transaction (testing)

## Monitoring & Scheduling

### **Automated Tasks**

1. **Email Monitoring** - Every 5 minutes
   - Checks Gmail for new bank transaction emails
   - Parses and records transactions
   - Matches with pending payments

2. **Daily Summary** - Every day at 8 AM
   - Sends daily transaction summary emails
   - Includes statistics and recent transactions

3. **Health Check** - Every hour
   - Verifies Gmail API connectivity
   - Logs system health status

4. **Cleanup** - Every day at 2 AM
   - Placeholder for old transaction cleanup

## Bank Email Patterns

The system supports various Vietnamese banks with configurable patterns:

### **Vietcombank**
- Sender: `<EMAIL>`
- Subject: `Thong bao bien dong so du`

### **Techcombank**
- Sender: `<EMAIL>`
- Subject: `Thong bao giao dich`

### **BIDV**
- Sender: `<EMAIL>`
- Subject: `Thong bao tai khoan`

## Transaction Flow

1. **Email Received** → Gmail API detects new email
2. **Email Parsed** → Extract transaction details
3. **Transaction Recorded** → Save to database
4. **Payment Matching** → Auto-match with pending payments
5. **Notification Sent** → Email notification to admin
6. **Status Updated** → Mark as processed/failed

## Security Considerations

1. **Gmail Credentials** - Stored securely, never committed to version control
2. **Email Access** - Read-only Gmail API access
3. **Database Security** - Encrypted sensitive data
4. **API Security** - Proper authentication and authorization
5. **Network Security** - HTTPS in production

## Troubleshooting

### **Common Issues**

1. **Gmail API Errors**
   - Check credentials.json file
   - Verify OAuth consent screen
   - Check API quotas

2. **Email Parsing Failures**
   - Review bank email patterns
   - Check transaction parsing logs
   - Verify email content format

3. **Database Connection Issues**
   - Verify database configuration
   - Check migration status
   - Review connection pool settings

### **Logging**

Enable debug logging for troubleshooting:

```properties
logging.level.com.dkgold.legacy.gms.service.GmailMonitoringService=DEBUG
logging.level.com.dkgold.legacy.gms.service.EmailMonitoringScheduler=DEBUG
logging.level.com.dkgold.legacy.gms.service.BankTransactionParserService=DEBUG
```

## Testing

### **Manual Testing**

1. **Trigger Email Monitoring**:
   ```bash
   curl -X POST http://localhost:8080/api/bank-transactions/monitor/trigger
   ```

2. **Check Monitoring Status**:
   ```bash
   curl http://localhost:8080/api/bank-transactions/monitoring/status
   ```

3. **Create Test Transaction**:
   ```bash
   curl -X POST http://localhost:8080/api/bank-transactions/manual \
     -H "Content-Type: application/json" \
     -d '{"transactionId":"TEST123","amount":1000000,"transactionType":"credit","bankConfigId":1}'
   ```

## Production Deployment

1. **Environment Setup** - Configure all environment variables
2. **Database Migration** - Run migration scripts
3. **Gmail API Setup** - Configure OAuth for production
4. **Monitoring Setup** - Configure application monitoring
5. **Security Review** - Review all security configurations

## Support

For issues or questions:
1. Check application logs
2. Review Gmail API quotas
3. Verify bank email patterns
4. Contact system administrator

---

**Note**: This system is designed for Vietnamese banking systems and may require customization for other regions.

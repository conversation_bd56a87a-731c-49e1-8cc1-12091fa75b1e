plugins {
	id 'java'
	id 'war'
	id 'org.springframework.boot' version '3.3.0'
	id 'io.spring.dependency-management' version '1.1.5'
}

group = 'com.dkgold'
version = '0.0.1-SNAPSHOT'

ext {
    querydslVersion = '5.0.0'
}

java {
	toolchain {
		languageVersion = JavaLanguageVersion.of(21)
	}
}

configurations {
	compileOnly {
		extendsFrom annotationProcessor
	}
}

repositories {
	mavenCentral()
}

dependencies {
	implementation 'org.springframework.boot:spring-boot-starter-actuator'
	implementation 'org.springframework.boot:spring-boot-starter-data-jdbc'
	implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
	implementation 'org.springframework.boot:spring-boot-starter-graphql'
	implementation 'org.springframework.boot:spring-boot-starter-oauth2-client'
	implementation 'org.springframework.boot:spring-boot-starter-oauth2-resource-server'
	implementation 'org.springframework.boot:spring-boot-starter-security'
	implementation 'org.springframework.boot:spring-boot-starter-web'
	implementation 'org.springframework.boot:spring-boot-starter-websocket'
	implementation 'org.springframework.boot:spring-boot-starter-mail'

	// Gmail API dependencies for real-time monitoring
	implementation 'com.google.apis:google-api-services-gmail:v1-rev20240520-2.0.0'
	implementation 'com.google.api-client:google-api-client:2.6.0'
	implementation 'com.google.oauth-client:google-oauth-client-jetty:1.36.0'
	implementation 'com.google.auth:google-auth-library-oauth2-http:1.23.0'

	implementation 'org.liquibase:liquibase-core:4.28.0'

	implementation 'org.apache.commons:commons-lang3:3.3'
	implementation 'org.modelmapper:modelmapper:3.2.1'
	implementation 'org.imgscalr:imgscalr-lib:4.2'
	implementation 'commons-io:commons-io:2.16.1'

	implementation "jakarta.servlet.jsp.jstl:jakarta.servlet.jsp.jstl-api:3.0.0"
	implementation 'org.glassfish.web:jakarta.servlet.jsp.jstl:3.0.1'
	providedRuntime 'org.apache.tomcat.embed:tomcat-embed-jasper'
	providedCompile group: 'javax.servlet', name: 'javax.servlet-api', version: '3.0.1'

	// QueryDSL
	implementation "com.querydsl:querydsl-jpa:${querydslVersion}:jakarta"
	annotationProcessor "com.querydsl:querydsl-apt:${querydslVersion}:jakarta"
	annotationProcessor "jakarta.persistence:jakarta.persistence-api"
	annotationProcessor "jakarta.annotation:jakarta.annotation-api"

	compileOnly 'org.projectlombok:lombok'
	developmentOnly 'org.springframework.boot:spring-boot-devtools'
	// this compose plugin is just for fast stated project. it automatically start docker-compose and configure the database
	// developmentOnly 'org.springframework.boot:spring-boot-docker-compose'
	runtimeOnly 'com.mysql:mysql-connector-j'
	annotationProcessor 'org.projectlombok:lombok'
	providedRuntime 'org.springframework.boot:spring-boot-starter-tomcat'

	testImplementation 'org.springframework.boot:spring-boot-starter-test'
	testImplementation 'org.springframework:spring-webflux'
	testImplementation 'org.springframework.graphql:spring-graphql-test'
	testImplementation 'org.springframework.security:spring-security-test'
	testRuntimeOnly 'org.junit.platform:junit-platform-launcher'
}
// tasks.named('test') {
// 	useJUnitPlatform()
// }

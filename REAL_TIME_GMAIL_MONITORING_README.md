# Real-time Gmail Monitoring with WebSocket Notifications

This system provides **real-time Gmail inbox monitoring** that immediately triggers **WebSocket notifications** when bank transaction emails arrive, replacing the previous 5-minute polling approach.

## 🚀 Features

### ✅ **Real-time Components**
- **Gmail Push Notifications** - Uses Gmail API push notifications via Google Cloud Pub/Sub
- **WebSocket Integration** - Immediate real-time notifications to connected clients
- **Webhook Controller** - Handles Gmail push notification callbacks
- **Bank Transaction WebSocket Service** - Manages real-time transaction notifications

### ✅ **WebSocket Topics**
- `/topic/bank/transactions/new` - New bank transactions
- `/topic/bank/balance/changes` - Balance change notifications  
- `/topic/bank/payments/matched` - Payment matching notifications
- `/topic/bank/notifications/all` - All bank notifications
- `/topic/bank/system/status` - System status updates

## 🏗️ Architecture

### **Real-time Flow**
1. **Gmail receives bank email** → Gmail API push notification
2. **Google Cloud Pub/Sub** → Webhook to your application
3. **GmailWebhookController** → Processes email immediately
4. **BankTransactionService** → Records transaction + triggers WebSocket
5. **WebSocket clients** → Receive real-time notifications instantly

### **Key Components**

#### **Services**
- `GmailPushNotificationService` - Sets up Gmail API push notifications
- `BankTransactionWebSocketService` - Sends real-time WebSocket notifications
- `GmailWebhookController` - Handles Gmail push notification webhooks
- `GmailPushNotificationScheduler` - Manages push notification lifecycle

#### **Models**
- `BankTransactionNotification` - WebSocket message model with notification types
- `BankConfigEntity` - Extended with email monitoring configuration fields

## 📋 Setup Instructions

### **1. Google Cloud Setup**

#### **Enable APIs**
```bash
# Enable required APIs
gcloud services enable gmail.googleapis.com
gcloud services enable pubsub.googleapis.com
```

#### **Create Pub/Sub Topic**
```bash
# Create topic for Gmail notifications
gcloud pubsub topics create gmail-notifications

# Create subscription (optional, for debugging)
gcloud pubsub subscriptions create gmail-notifications-sub --topic=gmail-notifications
```

#### **Set up OAuth Credentials**
1. Go to [Google Cloud Console](https://console.cloud.google.com)
2. Create OAuth 2.0 credentials (Desktop Application)
3. Download `credentials.json` to `src/main/resources/`

### **2. Application Configuration**

Update `application.properties`:
```properties
# Gmail API Configuration for Real-time Monitoring
app.gmail.credentials-path=credentials.json
app.gmail.tokens-directory=tokens
app.gmail.webhook.topic-name=projects/your-project-id/topics/gmail-notifications
app.gmail.webhook.base-url=https://your-domain.com

# Email Configuration
spring.mail.host=smtp.gmail.com
spring.mail.port=587
spring.mail.username=<EMAIL>
spring.mail.password=your-app-password
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true

# Admin notification email
app.notification.admin-email=<EMAIL>
```

### **3. Environment Variables**
```bash
# Required environment variables
export GMAIL_PUBSUB_TOPIC="projects/your-project-id/topics/gmail-notifications"
export WEBHOOK_BASE_URL="https://your-domain.com"
export MAIL_USERNAME="<EMAIL>"
export MAIL_PASSWORD="your-app-password"
export ADMIN_EMAIL="<EMAIL>"
```

### **4. Database Configuration**

Configure bank accounts for monitoring:
```sql
UPDATE bank_config SET 
    monitoring_email = '<EMAIL>',
    email_monitoring_enabled = 1,
    bank_email_sender = '<EMAIL>',
    transaction_email_subject_pattern = 'Transaction notification'
WHERE id = 1;
```

### **5. Webhook Endpoint Setup**

Your application must be accessible via HTTPS for Gmail push notifications:
- **Development**: Use ngrok or similar tunneling service
- **Production**: Configure proper HTTPS with valid SSL certificate

The webhook endpoint is: `https://your-domain.com/api/gmail/webhook/notification`

## 🧪 Testing

### **1. WebSocket Test Page**
Access the test page at: `http://localhost:8080/bank-notifications-test.html`

Features:
- Real-time WebSocket connection status
- Live notification display
- Statistics tracking
- Manual Gmail check trigger

### **2. API Endpoints**

#### **Manual Trigger**
```bash
POST /api/gmail/webhook/trigger-check
```

#### **Health Check**
```bash
GET /api/gmail/webhook/health
```

### **3. WebSocket Connection (JavaScript)**
```javascript
const socket = new SockJS('/ws/bank-notifications');
const stompClient = Stomp.over(socket);

stompClient.connect({}, function (frame) {
    // Subscribe to all notifications
    stompClient.subscribe('/topic/bank/notifications/all', function (message) {
        const notification = JSON.parse(message.body);
        console.log('Real-time notification:', notification);
    });
});
```

## 🔧 Configuration Options

### **Gmail Push Notification Settings**
- **Topic Name**: Google Cloud Pub/Sub topic for notifications
- **Webhook URL**: Your application's webhook endpoint
- **Refresh Interval**: Push notifications refresh every 6 days (Gmail limit is 7 days)

### **WebSocket Settings**
- **Endpoint**: `/ws/bank-notifications`
- **Topics**: Multiple topics for different notification types
- **Message Format**: JSON with transaction details and metadata

## 🚨 Troubleshooting

### **Common Issues**

#### **Gmail Push Notifications Not Working**
1. Check Google Cloud Pub/Sub topic exists
2. Verify OAuth credentials are valid
3. Ensure webhook URL is accessible via HTTPS
4. Check application logs for Gmail API errors

#### **WebSocket Connection Issues**
1. Verify WebSocket endpoint is accessible
2. Check browser console for connection errors
3. Ensure STOMP.js library is loaded
4. Test with the provided test page

#### **No Real-time Notifications**
1. Check if Gmail push notifications are set up
2. Verify bank email monitoring is enabled in database
3. Test with manual trigger endpoint
4. Check application logs for processing errors

### **Debugging Commands**
```bash
# Check Pub/Sub messages
gcloud pubsub subscriptions pull gmail-notifications-sub --limit=10

# Test webhook endpoint
curl -X POST https://your-domain.com/api/gmail/webhook/trigger-check

# Check application logs
tail -f logs/application.log | grep -i gmail
```

## 📊 Monitoring

### **Application Metrics**
- WebSocket connection count
- Notification processing time
- Gmail API call frequency
- Error rates and types

### **Logs to Monitor**
- Gmail push notification setup/refresh
- WebSocket connection/disconnection events
- Transaction processing and matching
- Email parsing errors

## 🔒 Security Considerations

1. **OAuth Credentials**: Keep `credentials.json` secure, never commit to version control
2. **Webhook Security**: Validate incoming webhook requests
3. **WebSocket Security**: Implement proper authentication for production
4. **Email Access**: Uses read-only Gmail API access
5. **HTTPS Required**: Gmail push notifications require HTTPS endpoints

## 🚀 Production Deployment

1. **SSL Certificate**: Ensure valid HTTPS certificate
2. **Load Balancing**: Configure WebSocket sticky sessions
3. **Monitoring**: Set up application monitoring and alerting
4. **Backup**: Regular backup of Gmail API tokens
5. **Rate Limits**: Monitor Gmail API quotas and limits

## 📈 Performance

- **Real-time**: Notifications delivered within seconds of email arrival
- **Scalable**: WebSocket connections support multiple concurrent clients
- **Efficient**: Push notifications eliminate polling overhead
- **Reliable**: Automatic reconnection and error handling

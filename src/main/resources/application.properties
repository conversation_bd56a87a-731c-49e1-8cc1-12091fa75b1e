spring.application.name=goldapp
management.endpoints.web.exposure.include=*
# spring.security.oauth2.resourceserver.jwt.issuer-uri=${KEYCLOAK_SERVER_URL}/realms/master
spring.security.oauth2.resourceserver.jwt.jwk-set-uri=${KEYCLOAK_SERVER_URL}/realms/master/protocol/openid-connect/certs
#spring.security.oauth2.resourceserver.jwt.public-key-location=classpath:public-key.pem
#logging.level.org.springframework.web: DEBUG
server.servlet.context-path=${CONTEXT_PATH}

db.password=${DATABASE_PASSWORD}
db.username=${DATABASE_USER}
db.url=${DATABASE_URL}
db.driver=com.mysql.jdbc.Driver
filecontent.path=${DATA_PATH}/image

spring.web.resources.static-locations=file:${DATA_PATH},classpath:/public/

spring.mvc.view.prefix: /WEB-INF/jsp/
spring.mvc.view.suffix: .jsp
#spring.jackson.time-zone=Asia/Ho_Chi_Minh


#KEYCLOAK_SERVER_URL=http://localhost:8081
#CONTEXT_PATH=CocaData
#DATABASE_PASSWORD=secret
#DATABASE_USER=myuser
#DATABASE_URL=***************************************************************************************************************************
#DATA_PATH=storage/app

integration.endpoint = ${INTEGRATION_SERVER_URL}

# Gmail API Configuration for Real-time Monitoring
app.gmail.credentials-path=credentials.json
app.gmail.tokens-directory=tokens
app.gmail.webhook.topic-name=${GMAIL_PUBSUB_TOPIC:projects/goldapp-system/topics/gmail-notifications}
app.gmail.webhook.base-url=${WEBHOOK_BASE_URL:https://your-domain.com}

# Email Configuration for notifications
spring.mail.host=${MAIL_HOST:smtp.gmail.com}
spring.mail.port=${MAIL_PORT:587}
spring.mail.username=${MAIL_USERNAME:<EMAIL>}
spring.mail.password=${MAIL_PASSWORD:your-app-password}
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true

# Admin notification email
app.notification.admin-email=${ADMIN_EMAIL:<EMAIL>}

-- Liquibase formatted SQL

-- changeset goldapp:********-001-extend-bank-config
-- Add email monitoring fields to bank_config table
ALTER TABLE bank_config 
ADD COLUMN monitoring_email VARCHAR(255),
ADD COLUMN gmail_credentials_path VARCHAR(500),
ADD COLUMN email_monitoring_enabled TINYINT(1) DEFAULT 0,
ADD COLUMN bank_email_sender VARCHAR(255),
ADD COLUMN transaction_email_subject_pattern VARCHAR(500);

-- changeset goldapp:********-002-create-bank-transaction-table
-- Create bank_transaction table for recording actual bank transactions
CREATE TABLE bank_transaction (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    transaction_id VARCHAR(100) NOT NULL UNIQUE,
    amount BIGINT NOT NULL,
    transaction_type VARCHAR(20) NOT NULL,
    description TEXT,
    sender_account VARCHAR(50),
    sender_name <PERSON><PERSON><PERSON><PERSON>(255),
    receiver_account VARCHAR(50),
    receiver_name <PERSON><PERSON><PERSON><PERSON>(255),
    transaction_date DATETIME NOT NULL,
    balance_after BIGINT,
    status VARCHAR(20) NOT NULL DEFAULT 'pending',
    email_message_id VARCHAR(255),
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME,
    bank_config_id BIGINT NOT NULL,
    payment_tracking_code VARCHAR(50),
    
    CONSTRAINT fk_bank_transaction_bank_config 
        FOREIGN KEY (bank_config_id) REFERENCES bank_config(id),
    CONSTRAINT fk_bank_transaction_payment_tracking 
        FOREIGN KEY (payment_tracking_code) REFERENCES payment_tracking(tracking_code),
    
    INDEX idx_bank_transaction_transaction_id (transaction_id),
    INDEX idx_bank_transaction_bank_config (bank_config_id),
    INDEX idx_bank_transaction_status (status),
    INDEX idx_bank_transaction_date (transaction_date),
    INDEX idx_bank_transaction_email_message (email_message_id),
    INDEX idx_bank_transaction_payment_tracking (payment_tracking_code)
);

-- changeset goldapp:********-003-add-sample-bank-config
-- Add sample bank configuration with email monitoring
INSERT INTO bank_config (
    bank_name, 
    bank_id, 
    branch_name, 
    account_number, 
    account_name, 
    is_default,
    monitoring_email,
    email_monitoring_enabled,
    bank_email_sender,
    transaction_email_subject_pattern
) VALUES (
    'Vietcombank',
    'VCB',
    'Ho Chi Minh City Branch',
    '**********',
    'CONG TY VANG DK',
    1,
    '<EMAIL>',
    1,
    '<EMAIL>',
    'Thong bao bien dong so du'
);

-- changeset goldapp:********-004-create-transaction-log-table
-- Create transaction processing log table for debugging
CREATE TABLE transaction_processing_log (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    email_message_id VARCHAR(255),
    bank_config_id BIGINT,
    processing_status VARCHAR(50),
    error_message TEXT,
    email_content TEXT,
    processed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_transaction_log_bank_config 
        FOREIGN KEY (bank_config_id) REFERENCES bank_config(id),
    
    INDEX idx_transaction_log_message_id (email_message_id),
    INDEX idx_transaction_log_bank_config (bank_config_id),
    INDEX idx_transaction_log_status (processing_status),
    INDEX idx_transaction_log_processed_at (processed_at)
);

-- changeset goldapp:********-005-add-constraints-and-triggers
-- Add trigger to update updated_at timestamp
DELIMITER $$
CREATE TRIGGER tr_bank_transaction_updated_at
    BEFORE UPDATE ON bank_transaction
    FOR EACH ROW
BEGIN
    SET NEW.updated_at = CURRENT_TIMESTAMP;
END$$
DELIMITER ;

-- Add check constraints for transaction types and statuses
ALTER TABLE bank_transaction 
ADD CONSTRAINT chk_transaction_type 
    CHECK (transaction_type IN ('credit', 'debit'));

ALTER TABLE bank_transaction 
ADD CONSTRAINT chk_transaction_status 
    CHECK (status IN ('pending', 'processed', 'failed'));

-- changeset goldapp:********-006-create-indexes-for-performance
-- Additional indexes for better query performance
CREATE INDEX idx_bank_transaction_amount ON bank_transaction(amount);
CREATE INDEX idx_bank_transaction_sender_account ON bank_transaction(sender_account);
CREATE INDEX idx_bank_transaction_created_at ON bank_transaction(created_at);
CREATE INDEX idx_bank_config_monitoring_enabled ON bank_config(email_monitoring_enabled);
CREATE INDEX idx_bank_config_default ON bank_config(is_default);

-- changeset goldapp:********-007-add-notification-settings
-- Add notification settings to bank_config
ALTER TABLE bank_config 
ADD COLUMN notification_enabled TINYINT(1) DEFAULT 1,
ADD COLUMN daily_summary_enabled TINYINT(1) DEFAULT 1,
ADD COLUMN balance_alert_threshold BIGINT DEFAULT 1000000;

-- changeset goldapp:********-008-create-email-template-table
-- Create table for email templates
CREATE TABLE email_template (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    template_name VARCHAR(100) NOT NULL UNIQUE,
    subject_template VARCHAR(500) NOT NULL,
    body_template TEXT NOT NULL,
    template_type VARCHAR(50) NOT NULL,
    is_active TINYINT(1) DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME,
    
    INDEX idx_email_template_name (template_name),
    INDEX idx_email_template_type (template_type),
    INDEX idx_email_template_active (is_active)
);

-- Insert default email templates
INSERT INTO email_template (template_name, subject_template, body_template, template_type) VALUES
('transaction_notification', 'New Bank Transaction - {{bankName}}', 
'A new bank transaction has been detected:\n\nBank: {{bankName}}\nAccount: {{accountNumber}}\nTransaction ID: {{transactionId}}\nAmount: {{amount}}\nType: {{transactionType}}\nDate: {{transactionDate}}\n\nThis is an automated notification from GoldApp Banking System.', 
'transaction'),

('payment_success', 'Payment Successfully Received - {{trackingCode}}', 
'Payment has been successfully received and matched:\n\nPayment Tracking Code: {{trackingCode}}\nExpected Amount: {{expectedAmount}}\nReceived Amount: {{receivedAmount}}\nBank: {{bankName}}\nTransaction ID: {{transactionId}}\n\nPayment Status: COMPLETED\n\nThis is an automated notification from GoldApp Banking System.', 
'payment'),

('daily_summary', 'Daily Transaction Summary - {{bankName}}', 
'Daily Transaction Summary for {{bankName}}:\n\nTotal Transactions: {{totalCount}}\nCredit Transactions: {{creditCount}} ({{totalCredit}})\nDebit Transactions: {{debitCount}} ({{totalDebit}})\nNet Change: {{netChange}}\n\nThis is an automated daily summary from GoldApp Banking System.', 
'summary');

-- Liquibase formatted SQL

-- changeset goldapp:********-001-extend-bank-config
-- Add email monitoring fields to bank_config table
ALTER TABLE bank_config 
ADD COLUMN monitoring_email VARCHAR(255),
ADD COLUMN gmail_credentials_path VARCHAR(500),
ADD COLUMN email_monitoring_enabled TINYINT(1) DEFAULT 0,
ADD COLUMN bank_email_sender VARCHAR(255),
ADD COLUMN transaction_email_subject_pattern VARCHAR(500);

-- changeset goldapp:********-002-create-bank-transaction-table
-- Create bank_transaction table for recording actual bank transactions
CREATE TABLE bank_transaction (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    transaction_id VARCHAR(100) NOT NULL UNIQUE,
    amount BIGINT NOT NULL,
    transaction_type VARCHAR(20) NOT NULL,
    description TEXT,
    sender_account VARCHAR(50),
    sender_name <PERSON><PERSON><PERSON><PERSON>(255),
    receiver_account VARCHAR(50),
    receiver_name <PERSON><PERSON><PERSON><PERSON>(255),
    transaction_date DATETIME NOT NULL,
    balance_after BIGINT,
    status VARCHAR(20) NOT NULL DEFAULT 'pending',
    email_message_id VARCHAR(255),
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME,
    bank_config_id BIGINT NOT NULL,
    payment_tracking_code VARCHAR(50),
    
    FOREIGN KEY (bank_config_id) REFERENCES bank_config(id),
    FOREIGN KEY (payment_tracking_code) REFERENCES payment_tracking(tracking_code),
    
    INDEX idx_transaction_id (transaction_id),
    INDEX idx_email_message_id (email_message_id),
    INDEX idx_status (status),
    INDEX idx_transaction_date (transaction_date),
    INDEX idx_created_at (created_at),
    INDEX idx_bank_config_id (bank_config_id)
);

-- changeset goldapp:********-003-add-sample-bank-config
-- Add sample bank configuration with email monitoring
INSERT INTO bank_config (
    bank_name, 
    bank_id, 
    branch_name, 
    account_number, 
    account_name, 
    is_default,
    monitoring_email,
    email_monitoring_enabled,
    bank_email_sender,
    transaction_email_subject_pattern
) VALUES (
    'Vietcombank',
    'VCB',
    'Ho Chi Minh City Branch',
    '**********',
    'CONG TY VANG DK',
    1,
    '<EMAIL>',
    1,
    '<EMAIL>',
    'Thong bao bien dong so du'
);

-- changeset goldapp:********-004-add-indexes-for-performance
-- Add additional indexes for better query performance
CREATE INDEX idx_bank_transaction_amount ON bank_transaction(amount);
CREATE INDEX idx_bank_transaction_type ON bank_transaction(transaction_type);
CREATE INDEX idx_bank_transaction_sender ON bank_transaction(sender_account);

-- changeset goldapp:********-005-add-bank-config-indexes
-- Add indexes to bank_config for email monitoring queries
CREATE INDEX idx_bank_config_monitoring ON bank_config(email_monitoring_enabled);
CREATE INDEX idx_bank_config_sender ON bank_config(bank_email_sender);

-- changeset goldapp:********-006-add-constraints
-- Add constraints for data integrity
ALTER TABLE bank_transaction 
ADD CONSTRAINT chk_transaction_type CHECK (transaction_type IN ('credit', 'debit')),
ADD CONSTRAINT chk_status CHECK (status IN ('pending', 'processed', 'failed')),
ADD CONSTRAINT chk_amount_positive CHECK (amount > 0);

-- changeset goldapp:********-007-add-notification-settings
-- Add notification settings to bank_config
ALTER TABLE bank_config 
ADD COLUMN notification_enabled TINYINT(1) DEFAULT 1,
ADD COLUMN daily_summary_enabled TINYINT(1) DEFAULT 1,
ADD COLUMN balance_alert_threshold BIGINT DEFAULT 1000000;

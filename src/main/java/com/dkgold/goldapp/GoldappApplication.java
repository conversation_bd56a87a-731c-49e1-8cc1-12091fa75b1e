package com.dkgold.goldapp;
import java.util.Map;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.actuate.web.exchanges.InMemoryHttpExchangeRepository;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Bean;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.web.client.RestTemplate;

@SpringBootApplication(
scanBasePackages = {
	"com.dkgold.goldapp.config",
	// "com.dkgold.legacy.cocadataaccess",
	"com.dkgold.legacy.gms",
	"com.dkgold.legacy.cocathanh.config",
	"com.dkgold.legacy.cocathanh.web"
})
@EnableAsync
@EnableScheduling
public class GoldappApplication {
	@Bean
	public InMemoryHttpExchangeRepository createTraceRepository() {
	return new InMemoryHttpExchangeRepository();
	}
	public static void main(String[] args) {
		System.out.println("++++++++++++++++++ debug env +++++++++++++++++++++");
		Map<String, String> envMap = System.getenv();
        for (Map.Entry<String, String> entry : envMap.entrySet()) {
            System.out.println(entry.getKey()+ "=" + entry.getValue());

        }
		SpringApplication.run(GoldappApplication.class, args);
	}

	@Bean
	public RestTemplate restTemplate() {
		return new RestTemplate();
	}
}


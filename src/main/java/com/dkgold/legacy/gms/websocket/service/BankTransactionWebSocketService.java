package com.dkgold.legacy.gms.websocket.service;

import com.dkgold.legacy.gms.data.BankTransactionEntity;
import com.dkgold.legacy.gms.websocket.model.BankTransactionNotification;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;

/**
 * WebSocket service for sending real-time bank transaction notifications
 */
@Service
public class BankTransactionWebSocketService {
    
    private static final Logger logger = LoggerFactory.getLogger(BankTransactionWebSocketService.class);
    
    // WebSocket topics
    private static final String TOPIC_NEW_TRANSACTIONS = "/topic/bank/transactions/new";
    private static final String TOPIC_BALANCE_CHANGES = "/topic/bank/balance/changes";
    private static final String TOPIC_PAYMENT_MATCHES = "/topic/bank/payments/matched";
    private static final String TOPIC_ALL_NOTIFICATIONS = "/topic/bank/notifications/all";
    
    @Autowired
    private SimpMessagingTemplate messagingTemplate;
    
    /**
     * Send notification for new bank transaction
     * @param transaction Bank transaction entity
     */
    public void sendNewTransactionNotification(BankTransactionEntity transaction) {
        try {
            BankTransactionNotification notification = BankTransactionNotification.newTransaction(transaction);
            
            // Send to specific topic
            messagingTemplate.convertAndSend(TOPIC_NEW_TRANSACTIONS, notification);
            
            // Send to general notifications topic
            messagingTemplate.convertAndSend(TOPIC_ALL_NOTIFICATIONS, notification);
            
            logger.info("Sent new transaction WebSocket notification: {}", notification.getTransactionId());
            
        } catch (Exception e) {
            logger.error("Failed to send new transaction WebSocket notification", e);
        }
    }
    
    /**
     * Send notification for balance change
     * @param transaction Bank transaction that caused balance change
     */
    public void sendBalanceChangeNotification(BankTransactionEntity transaction) {
        try {
            BankTransactionNotification notification = BankTransactionNotification.balanceChange(transaction);
            
            // Send to specific topic
            messagingTemplate.convertAndSend(TOPIC_BALANCE_CHANGES, notification);
            
            // Send to general notifications topic
            messagingTemplate.convertAndSend(TOPIC_ALL_NOTIFICATIONS, notification);
            
            logger.info("Sent balance change WebSocket notification: {} - Balance: {}", 
                       notification.getTransactionId(), notification.getBalanceAfter());
            
        } catch (Exception e) {
            logger.error("Failed to send balance change WebSocket notification", e);
        }
    }
    
    /**
     * Send notification when payment is matched
     * @param transaction Bank transaction that matched a payment
     */
    public void sendPaymentMatchedNotification(BankTransactionEntity transaction) {
        try {
            BankTransactionNotification notification = BankTransactionNotification.paymentMatched(transaction);
            
            // Send to specific topic
            messagingTemplate.convertAndSend(TOPIC_PAYMENT_MATCHES, notification);
            
            // Send to general notifications topic
            messagingTemplate.convertAndSend(TOPIC_ALL_NOTIFICATIONS, notification);
            
            logger.info("Sent payment matched WebSocket notification: {}", notification.getTransactionId());
            
        } catch (Exception e) {
            logger.error("Failed to send payment matched WebSocket notification", e);
        }
    }
    
    /**
     * Send notification for failed transaction
     * @param transaction Failed bank transaction
     */
    public void sendTransactionFailedNotification(BankTransactionEntity transaction) {
        try {
            BankTransactionNotification notification = BankTransactionNotification.transactionFailed(transaction);
            
            // Send to general notifications topic
            messagingTemplate.convertAndSend(TOPIC_ALL_NOTIFICATIONS, notification);
            
            logger.info("Sent transaction failed WebSocket notification: {}", notification.getTransactionId());
            
        } catch (Exception e) {
            logger.error("Failed to send transaction failed WebSocket notification", e);
        }
    }
    
    /**
     * Send custom notification
     * @param notification Custom bank transaction notification
     */
    public void sendCustomNotification(BankTransactionNotification notification) {
        try {
            messagingTemplate.convertAndSend(TOPIC_ALL_NOTIFICATIONS, notification);
            logger.info("Sent custom WebSocket notification: {}", notification);
        } catch (Exception e) {
            logger.error("Failed to send custom WebSocket notification", e);
        }
    }
    
    /**
     * Send notification to specific bank account subscribers
     * @param bankAccountNumber Bank account number
     * @param notification Notification to send
     */
    public void sendNotificationToAccount(String bankAccountNumber, BankTransactionNotification notification) {
        try {
            String accountTopic = "/topic/bank/account/" + bankAccountNumber + "/notifications";
            messagingTemplate.convertAndSend(accountTopic, notification);
            
            logger.info("Sent account-specific WebSocket notification to {}: {}", 
                       bankAccountNumber, notification.getTransactionId());
            
        } catch (Exception e) {
            logger.error("Failed to send account-specific WebSocket notification", e);
        }
    }
    
    /**
     * Send system status notification
     * @param message Status message
     */
    public void sendSystemStatusNotification(String message) {
        try {
            BankTransactionNotification notification = new BankTransactionNotification();
            notification.setType(BankTransactionNotification.NotificationType.NEW_TRANSACTION);
            notification.setDescription(message);
            
            messagingTemplate.convertAndSend("/topic/bank/system/status", notification);
            logger.info("Sent system status WebSocket notification: {}", message);
            
        } catch (Exception e) {
            logger.error("Failed to send system status WebSocket notification", e);
        }
    }
}

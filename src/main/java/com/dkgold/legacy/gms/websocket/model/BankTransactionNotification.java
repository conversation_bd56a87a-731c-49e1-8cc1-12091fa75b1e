package com.dkgold.legacy.gms.websocket.model;

import com.dkgold.legacy.gms.data.BankTransactionEntity;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDateTime;

/**
 * WebSocket message model for bank transaction notifications
 */
public class BankTransactionNotification {
    
    public enum NotificationType {
        NEW_TRANSACTION,
        BALANCE_CHANGE,
        PAYMENT_MATCHED,
        TRANSACTION_FAILED
    }
    
    private String id;
    private NotificationType type;
    private String transactionId;
    private Long amount;
    private String transactionType;
    private String description;
    private String senderName;
    private String senderAccount;
    private String bankName;
    private String accountNumber;
    private Long balanceAfter;
    private String status;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime transactionDate;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime notificationTime;
    
    // Constructors
    public BankTransactionNotification() {
        this.notificationTime = LocalDateTime.now();
    }
    
    public BankTransactionNotification(NotificationType type, BankTransactionEntity transaction) {
        this();
        this.type = type;
        this.id = transaction.getId() != null ? transaction.getId().toString() : null;
        this.transactionId = transaction.getTransactionId();
        this.amount = transaction.getAmount();
        this.transactionType = transaction.getTransactionType();
        this.description = transaction.getDescription();
        this.senderName = transaction.getSenderName();
        this.senderAccount = transaction.getSenderAccount();
        this.balanceAfter = transaction.getBalanceAfter();
        this.status = transaction.getStatus();
        this.transactionDate = transaction.getTransactionDate();
        
        if (transaction.getBankConfig() != null) {
            this.bankName = transaction.getBankConfig().getBankName();
            this.accountNumber = transaction.getBankConfig().getAccountNumber();
        }
    }
    
    // Static factory methods
    public static BankTransactionNotification newTransaction(BankTransactionEntity transaction) {
        return new BankTransactionNotification(NotificationType.NEW_TRANSACTION, transaction);
    }
    
    public static BankTransactionNotification balanceChange(BankTransactionEntity transaction) {
        return new BankTransactionNotification(NotificationType.BALANCE_CHANGE, transaction);
    }
    
    public static BankTransactionNotification paymentMatched(BankTransactionEntity transaction) {
        return new BankTransactionNotification(NotificationType.PAYMENT_MATCHED, transaction);
    }
    
    public static BankTransactionNotification transactionFailed(BankTransactionEntity transaction) {
        return new BankTransactionNotification(NotificationType.TRANSACTION_FAILED, transaction);
    }
    
    // Getters and Setters
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public NotificationType getType() {
        return type;
    }
    
    public void setType(NotificationType type) {
        this.type = type;
    }
    
    public String getTransactionId() {
        return transactionId;
    }
    
    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }
    
    public Long getAmount() {
        return amount;
    }
    
    public void setAmount(Long amount) {
        this.amount = amount;
    }
    
    public String getTransactionType() {
        return transactionType;
    }
    
    public void setTransactionType(String transactionType) {
        this.transactionType = transactionType;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public String getSenderName() {
        return senderName;
    }
    
    public void setSenderName(String senderName) {
        this.senderName = senderName;
    }
    
    public String getSenderAccount() {
        return senderAccount;
    }
    
    public void setSenderAccount(String senderAccount) {
        this.senderAccount = senderAccount;
    }
    
    public String getBankName() {
        return bankName;
    }
    
    public void setBankName(String bankName) {
        this.bankName = bankName;
    }
    
    public String getAccountNumber() {
        return accountNumber;
    }
    
    public void setAccountNumber(String accountNumber) {
        this.accountNumber = accountNumber;
    }
    
    public Long getBalanceAfter() {
        return balanceAfter;
    }
    
    public void setBalanceAfter(Long balanceAfter) {
        this.balanceAfter = balanceAfter;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    public LocalDateTime getTransactionDate() {
        return transactionDate;
    }
    
    public void setTransactionDate(LocalDateTime transactionDate) {
        this.transactionDate = transactionDate;
    }
    
    public LocalDateTime getNotificationTime() {
        return notificationTime;
    }
    
    public void setNotificationTime(LocalDateTime notificationTime) {
        this.notificationTime = notificationTime;
    }
    
    @Override
    public String toString() {
        return "BankTransactionNotification{" +
                "type=" + type +
                ", transactionId='" + transactionId + '\'' +
                ", amount=" + amount +
                ", bankName='" + bankName + '\'' +
                ", status='" + status + '\'' +
                '}';
    }
}

package com.dkgold.legacy.gms.service;

import com.dkgold.legacy.gms.data.BankTransactionEntity;
import com.dkgold.legacy.gms.data.PaymentTrackingEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.text.NumberFormat;
import java.time.format.DateTimeFormatter;
import java.util.Locale;

@Service
public class EmailNotificationService {
    
    private static final Logger logger = LoggerFactory.getLogger(EmailNotificationService.class);
    
    @Autowired
    private JavaMailSender mailSender;
    
    @Value("${spring.mail.username:<EMAIL>}")
    private String fromEmail;
    
    @Value("${app.notification.admin-email:<EMAIL>}")
    private String adminEmail;
    
    private final NumberFormat currencyFormatter = NumberFormat.getCurrencyInstance(new Locale("vi", "VN"));
    private final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss");
    
    /**
     * Send notification about new bank transaction
     * @param transaction Bank transaction
     */
    @Async
    public void sendTransactionNotification(BankTransactionEntity transaction) {
        try {
            SimpleMailMessage message = new SimpleMailMessage();
            message.setFrom(fromEmail);
            message.setTo(adminEmail);
            message.setSubject("New Bank Transaction Detected - " + transaction.getBankConfig().getBankName());
            
            StringBuilder body = new StringBuilder();
            body.append("A new bank transaction has been detected:\n\n");
            body.append("Bank: ").append(transaction.getBankConfig().getBankName()).append("\n");
            body.append("Account: ").append(transaction.getBankConfig().getAccountNumber()).append("\n");
            body.append("Transaction ID: ").append(transaction.getTransactionId()).append("\n");
            body.append("Amount: ").append(formatCurrency(transaction.getAmount())).append("\n");
            body.append("Type: ").append(transaction.getTransactionType().toUpperCase()).append("\n");
            body.append("Date: ").append(transaction.getTransactionDate().format(dateFormatter)).append("\n");
            
            if (transaction.getSenderName() != null) {
                body.append("Sender: ").append(transaction.getSenderName()).append("\n");
            }
            if (transaction.getSenderAccount() != null) {
                body.append("Sender Account: ").append(transaction.getSenderAccount()).append("\n");
            }
            if (transaction.getDescription() != null) {
                body.append("Description: ").append(transaction.getDescription()).append("\n");
            }
            if (transaction.getBalanceAfter() != null) {
                body.append("Balance After: ").append(formatCurrency(transaction.getBalanceAfter())).append("\n");
            }
            
            body.append("\nStatus: ").append(transaction.getStatus().toUpperCase()).append("\n");
            body.append("\nThis is an automated notification from GoldApp Banking System.");
            
            message.setText(body.toString());
            mailSender.send(message);
            
            logger.info("Sent transaction notification email for transaction: {}", transaction.getTransactionId());
            
        } catch (Exception e) {
            logger.error("Failed to send transaction notification email", e);
        }
    }
    
    /**
     * Send notification when payment is successfully matched
     * @param payment Payment tracking entity
     * @param transaction Bank transaction entity
     */
    @Async
    public void sendPaymentSuccessNotification(PaymentTrackingEntity payment, BankTransactionEntity transaction) {
        try {
            SimpleMailMessage message = new SimpleMailMessage();
            message.setFrom(fromEmail);
            message.setTo(adminEmail);
            message.setSubject("Payment Successfully Received - " + payment.getTrackingCode());
            
            StringBuilder body = new StringBuilder();
            body.append("Payment has been successfully received and matched:\n\n");
            body.append("Payment Tracking Code: ").append(payment.getTrackingCode()).append("\n");
            body.append("Expected Amount: ").append(formatCurrency(payment.getAmount())).append("\n");
            body.append("Received Amount: ").append(formatCurrency(transaction.getAmount())).append("\n");
            body.append("Bank: ").append(transaction.getBankConfig().getBankName()).append("\n");
            body.append("Transaction ID: ").append(transaction.getTransactionId()).append("\n");
            body.append("Transaction Date: ").append(transaction.getTransactionDate().format(dateFormatter)).append("\n");
            
            if (transaction.getSenderName() != null) {
                body.append("Sender: ").append(transaction.getSenderName()).append("\n");
            }
            if (transaction.getDescription() != null) {
                body.append("Description: ").append(transaction.getDescription()).append("\n");
            }
            
            body.append("\nPayment Status: COMPLETED\n");
            body.append("\nThis is an automated notification from GoldApp Banking System.");
            
            message.setText(body.toString());
            mailSender.send(message);
            
            logger.info("Sent payment success notification for tracking code: {}", payment.getTrackingCode());
            
        } catch (Exception e) {
            logger.error("Failed to send payment success notification email", e);
        }
    }
    
    /**
     * Send balance change notification
     * @param transaction Bank transaction that caused balance change
     */
    @Async
    public void sendBalanceChangeNotification(BankTransactionEntity transaction) {
        try {
            SimpleMailMessage message = new SimpleMailMessage();
            message.setFrom(fromEmail);
            message.setTo(adminEmail);
            message.setSubject("Bank Balance Changed - " + transaction.getBankConfig().getBankName());
            
            StringBuilder body = new StringBuilder();
            body.append("Bank account balance has changed:\n\n");
            body.append("Bank: ").append(transaction.getBankConfig().getBankName()).append("\n");
            body.append("Account: ").append(transaction.getBankConfig().getAccountNumber()).append("\n");
            body.append("Transaction Type: ").append(transaction.getTransactionType().toUpperCase()).append("\n");
            body.append("Amount: ").append(formatCurrency(transaction.getAmount())).append("\n");
            body.append("Date: ").append(transaction.getTransactionDate().format(dateFormatter)).append("\n");
            
            if (transaction.getBalanceAfter() != null) {
                body.append("New Balance: ").append(formatCurrency(transaction.getBalanceAfter())).append("\n");
            }
            
            body.append("\nThis is an automated notification from GoldApp Banking System.");
            
            message.setText(body.toString());
            mailSender.send(message);
            
            logger.info("Sent balance change notification for account: {}", transaction.getBankConfig().getAccountNumber());
            
        } catch (Exception e) {
            logger.error("Failed to send balance change notification email", e);
        }
    }
    
    /**
     * Send daily transaction summary
     * @param transactions List of transactions for the day
     * @param bankAccountName Bank account name
     */
    @Async
    public void sendDailyTransactionSummary(java.util.List<BankTransactionEntity> transactions, String bankAccountName) {
        try {
            if (transactions.isEmpty()) {
                return;
            }
            
            SimpleMailMessage message = new SimpleMailMessage();
            message.setFrom(fromEmail);
            message.setTo(adminEmail);
            message.setSubject("Daily Transaction Summary - " + bankAccountName);
            
            StringBuilder body = new StringBuilder();
            body.append("Daily Transaction Summary for ").append(bankAccountName).append(":\n\n");
            
            long totalCredit = 0;
            long totalDebit = 0;
            int creditCount = 0;
            int debitCount = 0;
            
            for (BankTransactionEntity transaction : transactions) {
                if (BankTransactionEntity.TYPE_CREDIT.equals(transaction.getTransactionType())) {
                    totalCredit += transaction.getAmount();
                    creditCount++;
                } else {
                    totalDebit += transaction.getAmount();
                    debitCount++;
                }
            }
            
            body.append("Total Transactions: ").append(transactions.size()).append("\n");
            body.append("Credit Transactions: ").append(creditCount).append(" (").append(formatCurrency(totalCredit)).append(")\n");
            body.append("Debit Transactions: ").append(debitCount).append(" (").append(formatCurrency(totalDebit)).append(")\n");
            body.append("Net Change: ").append(formatCurrency(totalCredit - totalDebit)).append("\n\n");
            
            body.append("Recent Transactions:\n");
            for (int i = 0; i < Math.min(10, transactions.size()); i++) {
                BankTransactionEntity transaction = transactions.get(i);
                body.append("- ").append(transaction.getTransactionDate().format(dateFormatter))
                    .append(" | ").append(transaction.getTransactionType().toUpperCase())
                    .append(" | ").append(formatCurrency(transaction.getAmount()))
                    .append(" | ").append(transaction.getTransactionId()).append("\n");
            }
            
            body.append("\nThis is an automated daily summary from GoldApp Banking System.");
            
            message.setText(body.toString());
            mailSender.send(message);
            
            logger.info("Sent daily transaction summary for: {}", bankAccountName);
            
        } catch (Exception e) {
            logger.error("Failed to send daily transaction summary email", e);
        }
    }
    
    /**
     * Format currency amount
     * @param amount Amount in VND
     * @return Formatted currency string
     */
    private String formatCurrency(Long amount) {
        if (amount == null) {
            return "N/A";
        }
        return String.format("%,d VND", amount);
    }
}

package com.dkgold.legacy.gms.service;

import com.google.api.client.auth.oauth2.Credential;
import com.google.api.client.extensions.java6.auth.oauth2.AuthorizationCodeInstalledApp;
import com.google.api.client.extensions.jetty.auth.oauth2.LocalServerReceiver;
import com.google.api.client.googleapis.auth.oauth2.GoogleAuthorizationCodeFlow;
import com.google.api.client.googleapis.auth.oauth2.GoogleClientSecrets;
import com.google.api.client.googleapis.javanet.GoogleNetHttpTransport;
import com.google.api.client.http.javanet.NetHttpTransport;
import com.google.api.client.json.JsonFactory;
import com.google.api.client.json.gson.GsonFactory;
import com.google.api.client.util.store.FileDataStoreFactory;
import com.google.api.services.gmail.Gmail;
import com.google.api.services.gmail.GmailScopes;
import com.google.api.services.gmail.model.WatchRequest;
import com.google.api.services.gmail.model.WatchResponse;
import com.dkgold.legacy.gms.data.BankConfigEntity;
import com.dkgold.legacy.gms.data.BankConfigRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.security.GeneralSecurityException;
import java.util.Collections;
import java.util.List;

/**
 * Service for setting up Gmail Push Notifications using Gmail API
 */
@Service
public class GmailPushNotificationService {
    
    private static final Logger logger = LoggerFactory.getLogger(GmailPushNotificationService.class);
    private static final String APPLICATION_NAME = "GoldApp Bank Transaction Monitor";
    private static final JsonFactory JSON_FACTORY = GsonFactory.getDefaultInstance();
    private static final String TOKENS_DIRECTORY_PATH = "tokens";
    private static final List<String> SCOPES = Collections.singletonList(GmailScopes.GMAIL_READONLY);
    private static final String CREDENTIALS_FILE_PATH = "/credentials.json";
    
    @Value("${app.gmail.webhook.topic-name:projects/your-project/topics/gmail-notifications}")
    private String pubsubTopicName;
    
    @Value("${app.gmail.webhook.base-url:https://your-domain.com}")
    private String webhookBaseUrl;
    
    @Autowired
    private BankConfigRepository bankConfigRepository;
    
    /**
     * Creates an authorized Credential object.
     * @param HTTP_TRANSPORT The network HTTP Transport.
     * @return An authorized Credential object.
     * @throws IOException If the credentials.json file cannot be found.
     */
    private Credential getCredentials(final NetHttpTransport HTTP_TRANSPORT) throws IOException {
        // Load client secrets.
        InputStream in = GmailPushNotificationService.class.getResourceAsStream(CREDENTIALS_FILE_PATH);
        if (in == null) {
            throw new FileNotFoundException("Resource not found: " + CREDENTIALS_FILE_PATH);
        }
        GoogleClientSecrets clientSecrets = GoogleClientSecrets.load(JSON_FACTORY, new InputStreamReader(in));

        // Build flow and trigger user authorization request.
        GoogleAuthorizationCodeFlow flow = new GoogleAuthorizationCodeFlow.Builder(
                HTTP_TRANSPORT, JSON_FACTORY, clientSecrets, SCOPES)
                .setDataStoreFactory(new FileDataStoreFactory(new java.io.File(TOKENS_DIRECTORY_PATH)))
                .setAccessType("offline")
                .build();
        LocalServerReceiver receiver = new LocalServerReceiver.Builder().setPort(8888).build();
        return new AuthorizationCodeInstalledApp(flow, receiver).authorize("user");
    }

    /**
     * Build and return an authorized Gmail client service.
     * @return An authorized Gmail client service
     * @throws IOException
     * @throws GeneralSecurityException
     */
    private Gmail getGmailService() throws IOException, GeneralSecurityException {
        final NetHttpTransport HTTP_TRANSPORT = GoogleNetHttpTransport.newTrustedTransport();
        return new Gmail.Builder(HTTP_TRANSPORT, JSON_FACTORY, getCredentials(HTTP_TRANSPORT))
                .setApplicationName(APPLICATION_NAME)
                .build();
    }
    
    /**
     * Set up Gmail push notifications for all configured bank accounts
     * @return true if setup was successful
     */
    public boolean setupPushNotifications() {
        try {
            Gmail service = getGmailService();
            
            // Get all banks with email monitoring enabled
            List<BankConfigEntity> monitoredBanks = bankConfigRepository.findAll().stream()
                    .filter(bank -> bank.getEmailMonitoringEnabled() != null && bank.getEmailMonitoringEnabled())
                    .toList();
            
            if (monitoredBanks.isEmpty()) {
                logger.warn("No banks configured for email monitoring");
                return false;
            }
            
            boolean allSuccessful = true;
            
            for (BankConfigEntity bankConfig : monitoredBanks) {
                try {
                    boolean success = setupPushNotificationForBank(service, bankConfig);
                    if (!success) {
                        allSuccessful = false;
                    }
                } catch (Exception e) {
                    logger.error("Failed to setup push notifications for bank: {}", bankConfig.getBankName(), e);
                    allSuccessful = false;
                }
            }
            
            return allSuccessful;
            
        } catch (Exception e) {
            logger.error("Failed to setup Gmail push notifications", e);
            return false;
        }
    }
    
    /**
     * Set up push notification for a specific bank
     * @param service Gmail service
     * @param bankConfig Bank configuration
     * @return true if successful
     */
    private boolean setupPushNotificationForBank(Gmail service, BankConfigEntity bankConfig) {
        try {
            // Create watch request
            WatchRequest watchRequest = new WatchRequest();
            watchRequest.setTopicName(pubsubTopicName);
            
            // Set label IDs to watch (INBOX by default)
            watchRequest.setLabelIds(Collections.singletonList("INBOX"));
            
            // Set label filter query to only watch emails from this bank
            StringBuilder queryBuilder = new StringBuilder();
            if (bankConfig.getBankEmailSender() != null && !bankConfig.getBankEmailSender().isEmpty()) {
                queryBuilder.append("from:").append(bankConfig.getBankEmailSender());
            }
            if (bankConfig.getTransactionEmailSubjectPattern() != null && !bankConfig.getTransactionEmailSubjectPattern().isEmpty()) {
                if (queryBuilder.length() > 0) {
                    queryBuilder.append(" ");
                }
                queryBuilder.append("subject:").append(bankConfig.getTransactionEmailSubjectPattern());
            }
            
            // Execute watch request
            WatchResponse watchResponse = service.users().watch("me", watchRequest).execute();
            
            logger.info("Successfully set up push notifications for bank: {} - Expiration: {}", 
                       bankConfig.getBankName(), watchResponse.getExpiration());
            
            return true;
            
        } catch (Exception e) {
            logger.error("Failed to setup push notification for bank: {}", bankConfig.getBankName(), e);
            return false;
        }
    }
    
    /**
     * Stop Gmail push notifications
     * @return true if successful
     */
    public boolean stopPushNotifications() {
        try {
            Gmail service = getGmailService();
            service.users().stop("me").execute();
            
            logger.info("Successfully stopped Gmail push notifications");
            return true;
            
        } catch (Exception e) {
            logger.error("Failed to stop Gmail push notifications", e);
            return false;
        }
    }
    
    /**
     * Check if push notifications are properly configured
     * @return true if configured
     */
    public boolean isPushNotificationConfigured() {
        try {
            // Check if we have the required configuration
            return pubsubTopicName != null && !pubsubTopicName.isEmpty() &&
                   webhookBaseUrl != null && !webhookBaseUrl.isEmpty();
        } catch (Exception e) {
            logger.error("Error checking push notification configuration", e);
            return false;
        }
    }
    
    /**
     * Refresh push notification setup (should be called periodically)
     * Gmail watch requests expire after 7 days
     */
    public void refreshPushNotifications() {
        logger.info("Refreshing Gmail push notifications...");
        
        try {
            // Stop existing notifications
            stopPushNotifications();
            
            // Set up new notifications
            boolean success = setupPushNotifications();
            
            if (success) {
                logger.info("Successfully refreshed Gmail push notifications");
            } else {
                logger.warn("Failed to refresh some Gmail push notifications");
            }
            
        } catch (Exception e) {
            logger.error("Error refreshing Gmail push notifications", e);
        }
    }
}

package com.dkgold.legacy.gms.service;

import com.google.api.client.auth.oauth2.Credential;
import com.google.api.client.extensions.java6.auth.oauth2.AuthorizationCodeInstalledApp;
import com.google.api.client.extensions.jetty.auth.oauth2.LocalServerReceiver;
import com.google.api.client.googleapis.auth.oauth2.GoogleAuthorizationCodeFlow;
import com.google.api.client.googleapis.auth.oauth2.GoogleClientSecrets;
import com.google.api.client.googleapis.javanet.GoogleNetHttpTransport;
import com.google.api.client.http.javanet.NetHttpTransport;
import com.google.api.client.json.JsonFactory;
import com.google.api.client.json.gson.GsonFactory;
import com.google.api.client.util.store.FileDataStoreFactory;
import com.google.api.services.gmail.Gmail;
import com.google.api.services.gmail.GmailScopes;
import com.google.api.services.gmail.model.ListMessagesResponse;
import com.google.api.services.gmail.model.Message;
import com.dkgold.legacy.gms.data.BankConfigEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.security.GeneralSecurityException;
import java.util.Collections;
import java.util.List;

@Service
public class GmailMonitoringService {
    
    private static final Logger logger = LoggerFactory.getLogger(GmailMonitoringService.class);
    private static final String APPLICATION_NAME = "GoldApp Bank Transaction Monitor";
    private static final JsonFactory JSON_FACTORY = GsonFactory.getDefaultInstance();
    private static final String TOKENS_DIRECTORY_PATH = "tokens";
    private static final List<String> SCOPES = Collections.singletonList(GmailScopes.GMAIL_READONLY);
    private static final String CREDENTIALS_FILE_PATH = "/credentials.json";

    /**
     * Creates an authorized Credential object.
     * @param HTTP_TRANSPORT The network HTTP Transport.
     * @return An authorized Credential object.
     * @throws IOException If the credentials.json file cannot be found.
     */
    private Credential getCredentials(final NetHttpTransport HTTP_TRANSPORT) throws IOException {
        // Load client secrets.
        InputStream in = GmailMonitoringService.class.getResourceAsStream(CREDENTIALS_FILE_PATH);
        if (in == null) {
            throw new FileNotFoundException("Resource not found: " + CREDENTIALS_FILE_PATH);
        }
        GoogleClientSecrets clientSecrets = GoogleClientSecrets.load(JSON_FACTORY, new InputStreamReader(in));

        // Build flow and trigger user authorization request.
        GoogleAuthorizationCodeFlow flow = new GoogleAuthorizationCodeFlow.Builder(
                HTTP_TRANSPORT, JSON_FACTORY, clientSecrets, SCOPES)
                .setDataStoreFactory(new FileDataStoreFactory(new java.io.File(TOKENS_DIRECTORY_PATH)))
                .setAccessType("offline")
                .build();
        LocalServerReceiver receiver = new LocalServerReceiver.Builder().setPort(8888).build();
        return new AuthorizationCodeInstalledApp(flow, receiver).authorize("user");
    }

    /**
     * Build and return an authorized Gmail client service.
     * @return An authorized Gmail client service
     * @throws IOException
     * @throws GeneralSecurityException
     */
    private Gmail getGmailService() throws IOException, GeneralSecurityException {
        final NetHttpTransport HTTP_TRANSPORT = GoogleNetHttpTransport.newTrustedTransport();
        return new Gmail.Builder(HTTP_TRANSPORT, JSON_FACTORY, getCredentials(HTTP_TRANSPORT))
                .setApplicationName(APPLICATION_NAME)
                .build();
    }

    /**
     * Get recent emails from bank for transaction monitoring
     * @param bankConfig Bank configuration containing email monitoring settings
     * @param maxResults Maximum number of messages to retrieve
     * @return List of Gmail messages
     */
    public List<Message> getRecentBankEmails(BankConfigEntity bankConfig, int maxResults) {
        try {
            Gmail service = getGmailService();
            String userId = "me";
            
            // Build query to filter emails from bank
            StringBuilder queryBuilder = new StringBuilder();
            if (bankConfig.getBankEmailSender() != null && !bankConfig.getBankEmailSender().isEmpty()) {
                queryBuilder.append("from:").append(bankConfig.getBankEmailSender());
            }
            if (bankConfig.getTransactionEmailSubjectPattern() != null && !bankConfig.getTransactionEmailSubjectPattern().isEmpty()) {
                if (queryBuilder.length() > 0) {
                    queryBuilder.append(" ");
                }
                queryBuilder.append("subject:").append(bankConfig.getTransactionEmailSubjectPattern());
            }
            
            // Add time filter for recent emails (last 24 hours)
            if (queryBuilder.length() > 0) {
                queryBuilder.append(" ");
            }
            queryBuilder.append("newer_than:1d");
            
            String query = queryBuilder.toString();
            logger.info("Searching Gmail with query: {}", query);
            
            ListMessagesResponse response = service.users().messages().list(userId)
                    .setQ(query)
                    .setMaxResults((long) maxResults)
                    .execute();
            
            List<Message> messages = response.getMessages();
            if (messages == null || messages.isEmpty()) {
                logger.info("No messages found for bank: {}", bankConfig.getBankName());
                return Collections.emptyList();
            }
            
            logger.info("Found {} messages for bank: {}", messages.size(), bankConfig.getBankName());
            return messages;
            
        } catch (IOException | GeneralSecurityException e) {
            logger.error("Error accessing Gmail API for bank: {}", bankConfig.getBankName(), e);
            return Collections.emptyList();
        }
    }

    /**
     * Get full message details including body
     * @param messageId Gmail message ID
     * @return Full message object
     */
    public Message getMessageDetails(String messageId) {
        try {
            Gmail service = getGmailService();
            return service.users().messages().get("me", messageId).execute();
        } catch (IOException | GeneralSecurityException e) {
            logger.error("Error getting message details for ID: {}", messageId, e);
            return null;
        }
    }

    /**
     * Extract plain text content from Gmail message
     * @param message Gmail message
     * @return Plain text content
     */
    public String extractTextContent(Message message) {
        if (message.getPayload() == null) {
            return "";
        }
        
        return extractTextFromPayload(message.getPayload());
    }
    
    private String extractTextFromPayload(com.google.api.services.gmail.model.MessagePart payload) {
        StringBuilder text = new StringBuilder();
        
        if (payload.getBody() != null && payload.getBody().getData() != null) {
            byte[] data = payload.getBody().decodeData();
            text.append(new String(data));
        }
        
        if (payload.getParts() != null) {
            for (com.google.api.services.gmail.model.MessagePart part : payload.getParts()) {
                if ("text/plain".equals(part.getMimeType()) && part.getBody() != null && part.getBody().getData() != null) {
                    byte[] data = part.getBody().decodeData();
                    text.append(new String(data));
                }
            }
        }
        
        return text.toString();
    }

    /**
     * Check if Gmail monitoring is properly configured
     * @param bankConfig Bank configuration
     * @return true if properly configured
     */
    public boolean isGmailMonitoringConfigured(BankConfigEntity bankConfig) {
        return bankConfig.getEmailMonitoringEnabled() != null && 
               bankConfig.getEmailMonitoringEnabled() &&
               bankConfig.getMonitoringEmail() != null && 
               !bankConfig.getMonitoringEmail().isEmpty();
    }
}

package com.dkgold.legacy.gms.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

/**
 * Scheduler service for managing Gmail push notifications
 * Sets up push notifications on startup and refreshes them periodically
 */
@Service
public class GmailPushNotificationScheduler {
    
    private static final Logger logger = LoggerFactory.getLogger(GmailPushNotificationScheduler.class);
    
    @Autowired
    private GmailPushNotificationService pushNotificationService;
    
    /**
     * Set up Gmail push notifications when application starts
     */
    @EventListener(ApplicationReadyEvent.class)
    public void setupPushNotificationsOnStartup() {
        logger.info("Setting up Gmail push notifications on application startup...");
        
        try {
            if (pushNotificationService.isPushNotificationConfigured()) {
                boolean success = pushNotificationService.setupPushNotifications();
                if (success) {
                    logger.info("Successfully set up Gmail push notifications on startup");
                } else {
                    logger.warn("Failed to set up some Gmail push notifications on startup");
                }
            } else {
                logger.warn("Gmail push notifications not configured - skipping setup");
            }
        } catch (Exception e) {
            logger.error("Error setting up Gmail push notifications on startup", e);
        }
    }
    
    /**
     * Refresh Gmail push notifications every 6 days
     * Gmail watch requests expire after 7 days, so we refresh before expiration
     */
    @Scheduled(fixedRate = 518400000) // 6 days in milliseconds
    public void refreshPushNotifications() {
        logger.info("Refreshing Gmail push notifications (scheduled)...");
        
        try {
            if (pushNotificationService.isPushNotificationConfigured()) {
                pushNotificationService.refreshPushNotifications();
            } else {
                logger.warn("Gmail push notifications not configured - skipping refresh");
            }
        } catch (Exception e) {
            logger.error("Error refreshing Gmail push notifications", e);
        }
    }
    
    /**
     * Health check for push notification system
     */
    @Scheduled(fixedRate = 3600000) // Every hour
    public void healthCheck() {
        try {
            boolean isConfigured = pushNotificationService.isPushNotificationConfigured();
            
            if (isConfigured) {
                logger.debug("Gmail push notification health check: CONFIGURED");
            } else {
                logger.debug("Gmail push notification health check: NOT CONFIGURED");
            }
            
        } catch (Exception e) {
            logger.error("Error in Gmail push notification health check", e);
        }
    }
}

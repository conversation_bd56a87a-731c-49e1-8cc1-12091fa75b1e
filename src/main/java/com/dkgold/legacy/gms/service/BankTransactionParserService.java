package com.dkgold.legacy.gms.service;

import com.dkgold.legacy.gms.data.BankConfigEntity;
import com.dkgold.legacy.gms.data.BankTransactionEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
public class BankTransactionParserService {
    
    private static final Logger logger = LoggerFactory.getLogger(BankTransactionParserService.class);
    
    // Common regex patterns for Vietnamese banks
    private static final Pattern TRANSACTION_ID_PATTERN = Pattern.compile("(?:Ma GD|Transaction ID|Ref|Reference)\\s*:?\\s*([A-Z0-9]+)", Pattern.CASE_INSENSITIVE);
    private static final Pattern AMOUNT_PATTERN = Pattern.compile("(?:So tien|Amount|Gia tri)\\s*:?\\s*([0-9,\\.]+)", Pattern.CASE_INSENSITIVE);
    private static final Pattern BALANCE_PATTERN = Pattern.compile("(?:So du|Balance|Remaining)\\s*:?\\s*([0-9,\\.]+)", Pattern.CASE_INSENSITIVE);
    private static final Pattern SENDER_NAME_PATTERN = Pattern.compile("(?:Nguoi gui|Sender|From)\\s*:?\\s*([^\\n\\r]+)", Pattern.CASE_INSENSITIVE);
    private static final Pattern SENDER_ACCOUNT_PATTERN = Pattern.compile("(?:TK gui|From account|Account)\\s*:?\\s*([0-9]+)", Pattern.CASE_INSENSITIVE);
    private static final Pattern DATE_PATTERN = Pattern.compile("(?:Thoi gian|Time|Date)\\s*:?\\s*([0-9]{1,2}[/-][0-9]{1,2}[/-][0-9]{2,4}\\s+[0-9]{1,2}:[0-9]{2}(?::[0-9]{2})?)", Pattern.CASE_INSENSITIVE);
    
    // Date formatters for Vietnamese banks
    private static final DateTimeFormatter[] DATE_FORMATTERS = {
        DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss"),
        DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm"),
        DateTimeFormatter.ofPattern("dd-MM-yyyy HH:mm:ss"),
        DateTimeFormatter.ofPattern("dd-MM-yyyy HH:mm"),
        DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"),
        DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")
    };
    
    /**
     * Parse bank transaction email content and create BankTransactionEntity
     * @param emailContent The email content to parse
     * @param emailMessageId Gmail message ID
     * @param bankConfig Bank configuration
     * @return Parsed BankTransactionEntity or null if parsing fails
     */
    public BankTransactionEntity parseTransactionEmail(String emailContent, String emailMessageId, BankConfigEntity bankConfig) {
        try {
            logger.debug("Parsing transaction email for bank: {}", bankConfig.getBankName());
            
            // Extract transaction details
            String transactionId = extractTransactionId(emailContent);
            Long amount = extractAmount(emailContent);
            String transactionType = determineTransactionType(emailContent);
            String description = extractDescription(emailContent);
            String senderAccount = extractSenderAccount(emailContent);
            String senderName = extractSenderName(emailContent);
            LocalDateTime transactionDate = extractTransactionDate(emailContent);
            Long balanceAfter = extractBalance(emailContent);
            
            // Validate required fields
            if (transactionId == null || amount == null || transactionType == null) {
                logger.warn("Failed to extract required transaction details from email. TransactionId: {}, Amount: {}, Type: {}", 
                           transactionId, amount, transactionType);
                return null;
            }
            
            // Create transaction entity
            BankTransactionEntity transaction = new BankTransactionEntity();
            transaction.setTransactionId(transactionId);
            transaction.setAmount(amount);
            transaction.setTransactionType(transactionType);
            transaction.setDescription(description);
            transaction.setSenderAccount(senderAccount);
            transaction.setSenderName(senderName);
            transaction.setTransactionDate(transactionDate != null ? transactionDate : LocalDateTime.now());
            transaction.setBalanceAfter(balanceAfter);
            transaction.setEmailMessageId(emailMessageId);
            transaction.setBankConfig(bankConfig);
            transaction.setStatus(BankTransactionEntity.STATUS_PENDING);
            
            logger.info("Successfully parsed transaction: ID={}, Amount={}, Type={}", 
                       transactionId, amount, transactionType);
            
            return transaction;
            
        } catch (Exception e) {
            logger.error("Error parsing transaction email for bank: {}", bankConfig.getBankName(), e);
            return null;
        }
    }
    
    private String extractTransactionId(String content) {
        Matcher matcher = TRANSACTION_ID_PATTERN.matcher(content);
        if (matcher.find()) {
            return matcher.group(1).trim();
        }
        return null;
    }
    
    private Long extractAmount(String content) {
        Matcher matcher = AMOUNT_PATTERN.matcher(content);
        if (matcher.find()) {
            String amountStr = matcher.group(1).replaceAll("[,\\.]", "");
            try {
                return Long.parseLong(amountStr);
            } catch (NumberFormatException e) {
                logger.warn("Failed to parse amount: {}", matcher.group(1));
            }
        }
        return null;
    }
    
    private Long extractBalance(String content) {
        Matcher matcher = BALANCE_PATTERN.matcher(content);
        if (matcher.find()) {
            String balanceStr = matcher.group(1).replaceAll("[,\\.]", "");
            try {
                return Long.parseLong(balanceStr);
            } catch (NumberFormatException e) {
                logger.warn("Failed to parse balance: {}", matcher.group(1));
            }
        }
        return null;
    }
    
    private String extractSenderName(String content) {
        Matcher matcher = SENDER_NAME_PATTERN.matcher(content);
        if (matcher.find()) {
            return matcher.group(1).trim();
        }
        return null;
    }
    
    private String extractSenderAccount(String content) {
        Matcher matcher = SENDER_ACCOUNT_PATTERN.matcher(content);
        if (matcher.find()) {
            return matcher.group(1).trim();
        }
        return null;
    }
    
    private LocalDateTime extractTransactionDate(String content) {
        Matcher matcher = DATE_PATTERN.matcher(content);
        if (matcher.find()) {
            String dateStr = matcher.group(1).trim();
            
            // Try different date formats
            for (DateTimeFormatter formatter : DATE_FORMATTERS) {
                try {
                    return LocalDateTime.parse(dateStr, formatter);
                } catch (DateTimeParseException e) {
                    // Continue to next formatter
                }
            }
            
            logger.warn("Failed to parse transaction date: {}", dateStr);
        }
        return null;
    }
    
    private String determineTransactionType(String content) {
        String lowerContent = content.toLowerCase();
        
        // Check for credit indicators (money coming in)
        if (lowerContent.contains("nhan tien") || 
            lowerContent.contains("chuyen den") ||
            lowerContent.contains("nap tien") ||
            lowerContent.contains("credit") ||
            lowerContent.contains("deposit") ||
            lowerContent.contains("incoming")) {
            return BankTransactionEntity.TYPE_CREDIT;
        }
        
        // Check for debit indicators (money going out)
        if (lowerContent.contains("chuyen di") ||
            lowerContent.contains("rut tien") ||
            lowerContent.contains("thanh toan") ||
            lowerContent.contains("debit") ||
            lowerContent.contains("withdrawal") ||
            lowerContent.contains("outgoing")) {
            return BankTransactionEntity.TYPE_DEBIT;
        }
        
        // Default to credit for bank notification emails (usually incoming transactions)
        return BankTransactionEntity.TYPE_CREDIT;
    }
    
    private String extractDescription(String content) {
        // Extract description/memo field
        Pattern descPattern = Pattern.compile("(?:Noi dung|Description|Memo|Note)\\s*:?\\s*([^\\n\\r]+)", Pattern.CASE_INSENSITIVE);
        Matcher matcher = descPattern.matcher(content);
        if (matcher.find()) {
            return matcher.group(1).trim();
        }
        
        // If no specific description field, return first 100 characters of content
        String cleanContent = content.replaceAll("\\s+", " ").trim();
        if (cleanContent.length() > 100) {
            return cleanContent.substring(0, 100) + "...";
        }
        return cleanContent;
    }
}

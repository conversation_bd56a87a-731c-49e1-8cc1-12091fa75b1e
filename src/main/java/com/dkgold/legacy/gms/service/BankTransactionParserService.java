package com.dkgold.legacy.gms.service;

import com.dkgold.legacy.gms.data.BankConfigEntity;
import com.dkgold.legacy.gms.data.BankTransactionEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
public class BankTransactionParserService {
    
    private static final Logger logger = LoggerFactory.getLogger(BankTransactionParserService.class);
    
    // Common patterns for Vietnamese banks
    private static final Pattern AMOUNT_PATTERN = Pattern.compile("(?:so tien|amount|gia tri giao dich)\\s*:?\\s*([0-9,\\.]+)\\s*(?:VND|vnd|đ)?", Pattern.CASE_INSENSITIVE);
    private static final Pattern TRANSACTION_ID_PATTERN = Pattern.compile("(?:ma giao dich|transaction id|ref|reference)\\s*:?\\s*([A-Z0-9]+)", Pattern.CASE_INSENSITIVE);
    private static final Pattern ACCOUNT_PATTERN = Pattern.compile("(?:tai khoan|account|tk)\\s*:?\\s*([0-9]+)", Pattern.CASE_INSENSITIVE);
    private static final Pattern SENDER_NAME_PATTERN = Pattern.compile("(?:nguoi gui|sender|from)\\s*:?\\s*([^\\n\\r]+)", Pattern.CASE_INSENSITIVE);
    private static final Pattern BALANCE_PATTERN = Pattern.compile("(?:so du|balance|remaining)\\s*:?\\s*([0-9,\\.]+)\\s*(?:VND|vnd|đ)?", Pattern.CASE_INSENSITIVE);
    private static final Pattern DATE_PATTERN = Pattern.compile("(?:ngay|date|time)\\s*:?\\s*([0-9]{1,2}[/-][0-9]{1,2}[/-][0-9]{2,4}\\s*[0-9]{1,2}:[0-9]{2}(?::[0-9]{2})?)", Pattern.CASE_INSENSITIVE);
    
    // Credit/Debit detection patterns
    private static final Pattern CREDIT_PATTERN = Pattern.compile("(?:nhan|receive|credit|\\+|cong|nap tien)", Pattern.CASE_INSENSITIVE);
    private static final Pattern DEBIT_PATTERN = Pattern.compile("(?:chuyen|transfer|debit|\\-|tru|rut tien)", Pattern.CASE_INSENSITIVE);
    
    /**
     * Parse bank transaction email content and create BankTransactionEntity
     * @param emailContent The email content to parse
     * @param emailMessageId Gmail message ID
     * @param bankConfig Bank configuration
     * @return Parsed BankTransactionEntity or null if parsing fails
     */
    public BankTransactionEntity parseTransactionEmail(String emailContent, String emailMessageId, BankConfigEntity bankConfig) {
        try {
            logger.debug("Parsing transaction email for bank: {}", bankConfig.getBankName());
            
            // Extract transaction details
            String transactionId = extractTransactionId(emailContent);
            Long amount = extractAmount(emailContent);
            String transactionType = determineTransactionType(emailContent);
            String description = extractDescription(emailContent);
            String senderAccount = extractSenderAccount(emailContent);
            String senderName = extractSenderName(emailContent);
            LocalDateTime transactionDate = extractTransactionDate(emailContent);
            Long balanceAfter = extractBalance(emailContent);
            
            // Validate required fields
            if (transactionId == null || amount == null || transactionType == null) {
                logger.warn("Failed to extract required transaction details from email. TransactionId: {}, Amount: {}, Type: {}", 
                           transactionId, amount, transactionType);
                return null;
            }
            
            // Create transaction entity
            BankTransactionEntity transaction = new BankTransactionEntity();
            transaction.setTransactionId(transactionId);
            transaction.setAmount(amount);
            transaction.setTransactionType(transactionType);
            transaction.setDescription(description != null ? description : "Bank transaction from email");
            transaction.setSenderAccount(senderAccount);
            transaction.setSenderName(senderName);
            transaction.setReceiverAccount(bankConfig.getAccountNumber());
            transaction.setReceiverName(bankConfig.getAccountName());
            transaction.setTransactionDate(transactionDate != null ? transactionDate : LocalDateTime.now());
            transaction.setBalanceAfter(balanceAfter);
            transaction.setEmailMessageId(emailMessageId);
            transaction.setBankConfig(bankConfig);
            transaction.setStatus(BankTransactionEntity.STATUS_PENDING);
            
            logger.info("Successfully parsed transaction: ID={}, Amount={}, Type={}", 
                       transactionId, amount, transactionType);
            
            return transaction;
            
        } catch (Exception e) {
            logger.error("Error parsing transaction email for bank: {}", bankConfig.getBankName(), e);
            return null;
        }
    }
    
    private String extractTransactionId(String content) {
        Matcher matcher = TRANSACTION_ID_PATTERN.matcher(content);
        if (matcher.find()) {
            return matcher.group(1).trim();
        }
        return null;
    }
    
    private Long extractAmount(String content) {
        Matcher matcher = AMOUNT_PATTERN.matcher(content);
        if (matcher.find()) {
            String amountStr = matcher.group(1).replaceAll("[,\\.]", "");
            try {
                return Long.parseLong(amountStr);
            } catch (NumberFormatException e) {
                logger.warn("Failed to parse amount: {}", matcher.group(1));
            }
        }
        return null;
    }
    
    private String determineTransactionType(String content) {
        if (CREDIT_PATTERN.matcher(content).find()) {
            return BankTransactionEntity.TYPE_CREDIT;
        } else if (DEBIT_PATTERN.matcher(content).find()) {
            return BankTransactionEntity.TYPE_DEBIT;
        }
        // Default to credit for incoming transaction notifications
        return BankTransactionEntity.TYPE_CREDIT;
    }
    
    private String extractDescription(String content) {
        // Try to extract description/memo from common patterns
        String[] lines = content.split("\\r?\\n");
        for (String line : lines) {
            if (line.toLowerCase().contains("noi dung") || 
                line.toLowerCase().contains("description") || 
                line.toLowerCase().contains("memo")) {
                String[] parts = line.split(":", 2);
                if (parts.length > 1) {
                    return parts[1].trim();
                }
            }
        }
        
        // If no specific description found, return first meaningful line
        for (String line : lines) {
            line = line.trim();
            if (line.length() > 10 && !line.toLowerCase().contains("dear") && 
                !line.toLowerCase().contains("bank") && !line.toLowerCase().contains("notification")) {
                return line;
            }
        }
        
        return null;
    }
    
    private String extractSenderAccount(String content) {
        Matcher matcher = ACCOUNT_PATTERN.matcher(content);
        if (matcher.find()) {
            return matcher.group(1).trim();
        }
        return null;
    }
    
    private String extractSenderName(String content) {
        Matcher matcher = SENDER_NAME_PATTERN.matcher(content);
        if (matcher.find()) {
            return matcher.group(1).trim();
        }
        return null;
    }
    
    private LocalDateTime extractTransactionDate(String content) {
        Matcher matcher = DATE_PATTERN.matcher(content);
        if (matcher.find()) {
            String dateStr = matcher.group(1).trim();
            try {
                // Try different date formats
                DateTimeFormatter[] formatters = {
                    DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss"),
                    DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm"),
                    DateTimeFormatter.ofPattern("dd-MM-yyyy HH:mm:ss"),
                    DateTimeFormatter.ofPattern("dd-MM-yyyy HH:mm"),
                    DateTimeFormatter.ofPattern("MM/dd/yyyy HH:mm:ss"),
                    DateTimeFormatter.ofPattern("MM/dd/yyyy HH:mm")
                };
                
                for (DateTimeFormatter formatter : formatters) {
                    try {
                        return LocalDateTime.parse(dateStr, formatter);
                    } catch (DateTimeParseException ignored) {
                        // Try next format
                    }
                }
            } catch (Exception e) {
                logger.warn("Failed to parse date: {}", dateStr);
            }
        }
        return null;
    }
    
    private Long extractBalance(String content) {
        Matcher matcher = BALANCE_PATTERN.matcher(content);
        if (matcher.find()) {
            String balanceStr = matcher.group(1).replaceAll("[,\\.]", "");
            try {
                return Long.parseLong(balanceStr);
            } catch (NumberFormatException e) {
                logger.warn("Failed to parse balance: {}", matcher.group(1));
            }
        }
        return null;
    }
}

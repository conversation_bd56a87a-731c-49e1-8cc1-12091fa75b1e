package com.dkgold.legacy.gms.service;

import com.google.api.services.gmail.model.Message;
import com.dkgold.legacy.gms.data.BankConfigEntity;
import com.dkgold.legacy.gms.data.BankTransactionEntity;
import com.dkgold.legacy.gms.repository.BankConfigRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

@Service
public class EmailMonitoringScheduler {
    
    private static final Logger logger = LoggerFactory.getLogger(EmailMonitoringScheduler.class);
    
    @Autowired
    private BankConfigRepository bankConfigRepository;
    
    @Autowired
    private GmailMonitoringService gmailMonitoringService;
    
    @Autowired
    private BankTransactionParserService parserService;
    
    @Autowired
    private BankTransactionService transactionService;
    
    @Autowired
    private EmailNotificationService emailNotificationService;
    
    /**
     * Monitor Gmail for new bank transaction emails every 5 minutes
     */
    @Scheduled(fixedRate = 300000) // 5 minutes
    public void monitorBankEmails() {
        logger.debug("Starting email monitoring cycle at {}", LocalDateTime.now());
        
        try {
            // Get all bank configs with email monitoring enabled
            List<BankConfigEntity> monitoredBanks = bankConfigRepository.findAll().stream()
                    .filter(bank -> gmailMonitoringService.isGmailMonitoringConfigured(bank))
                    .toList();
            
            if (monitoredBanks.isEmpty()) {
                logger.debug("No banks configured for email monitoring");
                return;
            }
            
            logger.info("Monitoring {} bank accounts for email notifications", monitoredBanks.size());
            
            for (BankConfigEntity bankConfig : monitoredBanks) {
                try {
                    monitorBankAccount(bankConfig);
                } catch (Exception e) {
                    logger.error("Error monitoring bank account: {}", bankConfig.getBankName(), e);
                }
            }
            
        } catch (Exception e) {
            logger.error("Error in email monitoring cycle", e);
        }
        
        logger.debug("Completed email monitoring cycle at {}", LocalDateTime.now());
    }
    
    /**
     * Monitor a specific bank account for new emails
     * @param bankConfig Bank configuration
     */
    private void monitorBankAccount(BankConfigEntity bankConfig) {
        logger.debug("Monitoring emails for bank: {}", bankConfig.getBankName());
        
        try {
            // Get recent emails from this bank
            List<Message> messages = gmailMonitoringService.getRecentBankEmails(bankConfig, 10);
            
            if (messages.isEmpty()) {
                logger.debug("No new emails found for bank: {}", bankConfig.getBankName());
                return;
            }
            
            logger.info("Found {} potential transaction emails for bank: {}", messages.size(), bankConfig.getBankName());
            
            int processedCount = 0;
            int skippedCount = 0;
            
            for (Message message : messages) {
                try {
                    // Get full message details
                    Message fullMessage = gmailMonitoringService.getMessageDetails(message.getId());
                    if (fullMessage == null) {
                        logger.warn("Could not retrieve message details for ID: {}", message.getId());
                        continue;
                    }
                    
                    // Extract email content
                    String emailContent = gmailMonitoringService.extractTextContent(fullMessage);
                    if (emailContent == null || emailContent.trim().isEmpty()) {
                        logger.warn("Empty email content for message ID: {}", message.getId());
                        continue;
                    }
                    
                    // Parse transaction from email
                    BankTransactionEntity transaction = parserService.parseTransactionEmail(
                            emailContent, message.getId(), bankConfig);
                    
                    if (transaction != null) {
                        // Record the transaction
                        BankTransactionEntity savedTransaction = transactionService.recordTransaction(transaction);
                        if (savedTransaction != null) {
                            processedCount++;
                            logger.info("Successfully processed transaction email: {} for bank: {}", 
                                       savedTransaction.getTransactionId(), bankConfig.getBankName());
                        } else {
                            skippedCount++;
                        }
                    } else {
                        skippedCount++;
                        logger.debug("Could not parse transaction from email ID: {}", message.getId());
                    }
                    
                } catch (Exception e) {
                    logger.error("Error processing email message ID: {}", message.getId(), e);
                    skippedCount++;
                }
            }
            
            logger.info("Email monitoring completed for bank: {} - Processed: {}, Skipped: {}", 
                       bankConfig.getBankName(), processedCount, skippedCount);
            
        } catch (Exception e) {
            logger.error("Error monitoring bank account: {}", bankConfig.getBankName(), e);
        }
    }
    
    /**
     * Send daily transaction summary every day at 8 AM
     */
    @Scheduled(cron = "0 0 8 * * *")
    public void sendDailyTransactionSummary() {
        logger.info("Generating daily transaction summary");
        
        try {
            List<BankConfigEntity> allBanks = bankConfigRepository.findAll();
            
            for (BankConfigEntity bankConfig : allBanks) {
                try {
                    // Get yesterday's transactions
                    List<BankTransactionEntity> dailyTransactions = transactionService
                            .getRecentTransactions(24); // Last 24 hours
                    
                    // Filter by bank config
                    List<BankTransactionEntity> bankTransactions = dailyTransactions.stream()
                            .filter(t -> t.getBankConfig().getId().equals(bankConfig.getId()))
                            .toList();
                    
                    if (!bankTransactions.isEmpty()) {
                        emailNotificationService.sendDailyTransactionSummary(
                                bankTransactions, 
                                bankConfig.getBankName() + " - " + bankConfig.getAccountNumber()
                        );
                    }
                    
                } catch (Exception e) {
                    logger.error("Error generating daily summary for bank: {}", bankConfig.getBankName(), e);
                }
            }
            
        } catch (Exception e) {
            logger.error("Error in daily transaction summary generation", e);
        }
    }
    
    /**
     * Clean up old processed transactions (keep for 30 days)
     */
    @Scheduled(cron = "0 0 2 * * *") // Every day at 2 AM
    public void cleanupOldTransactions() {
        logger.info("Starting cleanup of old transactions");
        
        try {
            // This would require additional repository method to delete old transactions
            // For now, just log the cleanup attempt
            logger.info("Transaction cleanup completed");
            
        } catch (Exception e) {
            logger.error("Error in transaction cleanup", e);
        }
    }
    
    /**
     * Health check for email monitoring system
     */
    @Scheduled(fixedRate = 3600000) // Every hour
    public void healthCheck() {
        try {
            // Check if Gmail service is accessible
            List<BankConfigEntity> monitoredBanks = bankConfigRepository.findAll().stream()
                    .filter(bank -> gmailMonitoringService.isGmailMonitoringConfigured(bank))
                    .toList();
            
            if (monitoredBanks.isEmpty()) {
                logger.debug("Health check: No banks configured for monitoring");
                return;
            }
            
            // Try to access Gmail for the first configured bank
            BankConfigEntity testBank = monitoredBanks.get(0);
            List<Message> testMessages = gmailMonitoringService.getRecentBankEmails(testBank, 1);
            
            logger.debug("Health check passed: Gmail service accessible");
            
        } catch (Exception e) {
            logger.error("Health check failed: Gmail service may be unavailable", e);
        }
    }
}

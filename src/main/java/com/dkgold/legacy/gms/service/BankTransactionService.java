package com.dkgold.legacy.gms.service;

import com.dkgold.legacy.gms.data.BankConfigEntity;
import com.dkgold.legacy.gms.data.BankTransactionEntity;
import com.dkgold.legacy.gms.data.PaymentTrackingEntity;
import com.dkgold.legacy.gms.repository.BankTransactionRepository;
import com.dkgold.legacy.gms.repository.PaymentTrackingRepository;
import com.dkgold.legacy.gms.websocket.service.BankTransactionWebSocketService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Service
@Transactional
public class BankTransactionService {
    
    private static final Logger logger = LoggerFactory.getLogger(BankTransactionService.class);
    
    @Autowired
    private BankTransactionRepository bankTransactionRepository;
    
    @Autowired
    private PaymentTrackingRepository paymentTrackingRepository;
    
    @Autowired
    private EmailNotificationService emailNotificationService;
    
    @Autowired
    private BankTransactionWebSocketService webSocketService;
    
    /**
     * Record a new bank transaction
     * @param transaction Bank transaction to record
     * @return Saved transaction entity
     */
    public BankTransactionEntity recordTransaction(BankTransactionEntity transaction) {
        try {
            // Check if transaction already exists
            Optional<BankTransactionEntity> existing = bankTransactionRepository.findByTransactionId(transaction.getTransactionId());
            if (existing.isPresent()) {
                logger.info("Transaction already exists: {}", transaction.getTransactionId());
                return existing.get();
            }
            
            // Check for duplicate by email message ID
            if (transaction.getEmailMessageId() != null) {
                Optional<BankTransactionEntity> existingByEmail = bankTransactionRepository.findByEmailMessageId(transaction.getEmailMessageId());
                if (existingByEmail.isPresent()) {
                    logger.info("Transaction with same email message ID already exists: {}", transaction.getEmailMessageId());
                    return existingByEmail.get();
                }
            }
            
            // Save transaction
            BankTransactionEntity savedTransaction = bankTransactionRepository.save(transaction);
            logger.info("Recorded new bank transaction: ID={}, Amount={}, Type={}", 
                       savedTransaction.getTransactionId(), savedTransaction.getAmount(), savedTransaction.getTransactionType());
            
            // Send real-time WebSocket notification immediately
            webSocketService.sendNewTransactionNotification(savedTransaction);
            
            // Send balance change notification if balance is available
            if (savedTransaction.getBalanceAfter() != null) {
                webSocketService.sendBalanceChangeNotification(savedTransaction);
            }
            
            // Try to match with pending payment tracking
            matchWithPaymentTracking(savedTransaction);
            
            // Send email notification about new transaction
            emailNotificationService.sendTransactionNotification(savedTransaction);
            
            return savedTransaction;
            
        } catch (Exception e) {
            logger.error("Error recording bank transaction: {}", transaction.getTransactionId(), e);
            throw e;
        }
    }
    
    /**
     * Try to match bank transaction with pending payment tracking
     * @param transaction Bank transaction to match
     */
    private void matchWithPaymentTracking(BankTransactionEntity transaction) {
        try {
            // Only match credit transactions (incoming money)
            if (!BankTransactionEntity.TYPE_CREDIT.equals(transaction.getTransactionType())) {
                return;
            }
            
            // Find pending payment trackings with matching amount
            List<BankTransactionEntity> potentialMatches = bankTransactionRepository
                    .findPendingTransactionsByAmountAndDescription(transaction.getAmount(), "");
            
            // Try to find exact amount match in payment tracking
            List<PaymentTrackingEntity> pendingPayments = paymentTrackingRepository.findAll().stream()
                    .filter(pt -> PaymentTrackingEntity.STATUS_PENDING.equals(pt.getStatus()) && 
                                 pt.getAmount().equals(transaction.getAmount()))
                    .toList();
            
            if (!pendingPayments.isEmpty()) {
                // Match with the oldest pending payment
                PaymentTrackingEntity paymentToMatch = pendingPayments.get(0);
                
                // Update payment tracking
                paymentToMatch.setStatus(PaymentTrackingEntity.STATUS_SUCCESS);
                paymentToMatch.setReceiveAmount(transaction.getAmount());
                paymentTrackingRepository.save(paymentToMatch);
                
                // Link transaction to payment tracking
                transaction.setPaymentTracking(paymentToMatch);
                transaction.setStatus(BankTransactionEntity.STATUS_PROCESSED);
                bankTransactionRepository.save(transaction);
                
                logger.info("Matched bank transaction {} with payment tracking {}", 
                           transaction.getTransactionId(), paymentToMatch.getTrackingCode());
                
                // Send real-time WebSocket notification for payment match
                webSocketService.sendPaymentMatchedNotification(transaction);
                
                // Send success notification
                emailNotificationService.sendPaymentSuccessNotification(paymentToMatch, transaction);
            }
            
        } catch (Exception e) {
            logger.error("Error matching transaction with payment tracking: {}", transaction.getTransactionId(), e);
        }
    }
    
    /**
     * Get transactions for a specific bank account
     * @param bankConfig Bank configuration
     * @return List of transactions
     */
    public List<BankTransactionEntity> getTransactionsByBankConfig(BankConfigEntity bankConfig) {
        return bankTransactionRepository.findByBankConfigOrderByTransactionDateDesc(bankConfig);
    }
    
    /**
     * Get unprocessed transactions for a bank account
     * @param bankConfig Bank configuration
     * @return List of unprocessed transactions
     */
    public List<BankTransactionEntity> getUnprocessedTransactions(BankConfigEntity bankConfig) {
        return bankTransactionRepository.findUnprocessedTransactionsByBankConfig(bankConfig);
    }
    
    /**
     * Get recent transactions for dashboard
     * @param hours Number of hours to look back
     * @return List of recent transactions
     */
    public List<BankTransactionEntity> getRecentTransactions(int hours) {
        LocalDateTime since = LocalDateTime.now().minusHours(hours);
        return bankTransactionRepository.findRecentTransactions(since);
    }
    
    /**
     * Update transaction status
     * @param transactionId Transaction ID
     * @param status New status
     * @return Updated transaction
     */
    public BankTransactionEntity updateTransactionStatus(String transactionId, String status) {
        Optional<BankTransactionEntity> transactionOpt = bankTransactionRepository.findByTransactionId(transactionId);
        if (transactionOpt.isPresent()) {
            BankTransactionEntity transaction = transactionOpt.get();
            String oldStatus = transaction.getStatus();
            transaction.setStatus(status);
            BankTransactionEntity updatedTransaction = bankTransactionRepository.save(transaction);
            
            // Send WebSocket notification for status change
            if (BankTransactionEntity.STATUS_FAILED.equals(status)) {
                webSocketService.sendTransactionFailedNotification(updatedTransaction);
            }
            
            logger.info("Updated transaction {} status from {} to {}", transactionId, oldStatus, status);
            return updatedTransaction;
        }
        throw new RuntimeException("Transaction not found: " + transactionId);
    }
    
    /**
     * Get transaction statistics
     * @return Transaction statistics
     */
    public TransactionStatistics getTransactionStatistics() {
        long pendingCount = bankTransactionRepository.countByStatus(BankTransactionEntity.STATUS_PENDING);
        long processedCount = bankTransactionRepository.countByStatus(BankTransactionEntity.STATUS_PROCESSED);
        long failedCount = bankTransactionRepository.countByStatus(BankTransactionEntity.STATUS_FAILED);
        
        return new TransactionStatistics(pendingCount, processedCount, failedCount);
    }
    
    /**
     * Manual transaction matching for admin
     * @param transactionId Bank transaction ID
     * @param paymentTrackingCode Payment tracking code
     * @return true if matched successfully
     */
    public boolean manualMatchTransaction(String transactionId, String paymentTrackingCode) {
        try {
            Optional<BankTransactionEntity> transactionOpt = bankTransactionRepository.findByTransactionId(transactionId);
            Optional<PaymentTrackingEntity> paymentOpt = paymentTrackingRepository.findById(paymentTrackingCode);
            
            if (transactionOpt.isPresent() && paymentOpt.isPresent()) {
                BankTransactionEntity transaction = transactionOpt.get();
                PaymentTrackingEntity payment = paymentOpt.get();
                
                // Update payment tracking
                payment.setStatus(PaymentTrackingEntity.STATUS_SUCCESS);
                payment.setReceiveAmount(transaction.getAmount());
                paymentTrackingRepository.save(payment);
                
                // Link transaction
                transaction.setPaymentTracking(payment);
                transaction.setStatus(BankTransactionEntity.STATUS_PROCESSED);
                bankTransactionRepository.save(transaction);
                
                // Send real-time WebSocket notification for manual match
                webSocketService.sendPaymentMatchedNotification(transaction);
                
                logger.info("Manually matched transaction {} with payment {}", transactionId, paymentTrackingCode);
                return true;
            }
            
            return false;
            
        } catch (Exception e) {
            logger.error("Error in manual transaction matching", e);
            return false;
        }
    }
    
    /**
     * Transaction statistics class
     */
    public static class TransactionStatistics {
        private final long pendingCount;
        private final long processedCount;
        private final long failedCount;
        
        public TransactionStatistics(long pendingCount, long processedCount, long failedCount) {
            this.pendingCount = pendingCount;
            this.processedCount = processedCount;
            this.failedCount = failedCount;
        }
        
        public long getPendingCount() { return pendingCount; }
        public long getProcessedCount() { return processedCount; }
        public long getFailedCount() { return failedCount; }
        public long getTotalCount() { return pendingCount + processedCount + failedCount; }
    }
}

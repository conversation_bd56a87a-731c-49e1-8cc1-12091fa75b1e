package com.dkgold.legacy.gms.web.controller;

import com.dkgold.legacy.gms.data.BankConfigEntity;
import com.dkgold.legacy.gms.data.BankConfigRepository;
import com.dkgold.legacy.gms.data.BankTransactionEntity;
import com.dkgold.legacy.gms.service.BankTransactionParserService;
import com.dkgold.legacy.gms.service.BankTransactionService;
import com.dkgold.legacy.gms.service.GmailMonitoringService;
import com.dkgold.legacy.gms.websocket.service.BankTransactionWebSocketService;
import com.google.api.services.gmail.model.Message;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * Controller to handle Gmail push notification webhooks
 * This receives real-time notifications when new emails arrive
 */
@RestController
@RequestMapping("/api/gmail/webhook")
public class GmailWebhookController {
    
    private static final Logger logger = LoggerFactory.getLogger(GmailWebhookController.class);
    
    @Autowired
    private BankConfigRepository bankConfigRepository;
    
    @Autowired
    private GmailMonitoringService gmailMonitoringService;
    
    @Autowired
    private BankTransactionParserService parserService;
    
    @Autowired
    private BankTransactionService transactionService;
    
    @Autowired
    private BankTransactionWebSocketService webSocketService;
    
    /**
     * Handle Gmail push notification webhook
     * This endpoint is called by Google Cloud Pub/Sub when new emails arrive
     */
    @PostMapping("/notification")
    public ResponseEntity<String> handleGmailNotification(@RequestBody Map<String, Object> payload) {
        try {
            logger.info("Received Gmail push notification: {}", payload);
            
            // Extract message data from Pub/Sub payload
            Map<String, Object> message = (Map<String, Object>) payload.get("message");
            if (message == null) {
                logger.warn("No message data in Gmail notification payload");
                return ResponseEntity.badRequest().body("Invalid payload");
            }
            
            // Decode the message data (it's base64 encoded)
            String data = (String) message.get("data");
            if (data == null) {
                logger.warn("No data in Gmail notification message");
                return ResponseEntity.badRequest().body("No message data");
            }
            
            // Process the notification immediately
            processGmailNotification(data);
            
            return ResponseEntity.ok("Notification processed");
            
        } catch (Exception e) {
            logger.error("Error processing Gmail push notification", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error processing notification");
        }
    }
    
    /**
     * Manual trigger endpoint for testing
     */
    @PostMapping("/trigger-check")
    public ResponseEntity<String> triggerManualCheck() {
        try {
            logger.info("Manual Gmail check triggered");
            
            // Get all monitored banks
            List<BankConfigEntity> monitoredBanks = bankConfigRepository.findAll().stream()
                    .filter(bank -> gmailMonitoringService.isGmailMonitoringConfigured(bank))
                    .toList();
            
            int processedCount = 0;
            
            for (BankConfigEntity bankConfig : monitoredBanks) {
                try {
                    int bankProcessedCount = processRecentEmailsForBank(bankConfig);
                    processedCount += bankProcessedCount;
                } catch (Exception e) {
                    logger.error("Error processing emails for bank: {}", bankConfig.getBankName(), e);
                }
            }
            
            return ResponseEntity.ok("Processed " + processedCount + " transactions");
            
        } catch (Exception e) {
            logger.error("Error in manual Gmail check", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error in manual check");
        }
    }
    
    /**
     * Health check endpoint
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> healthCheck() {
        try {
            Map<String, Object> health = Map.of(
                "status", "healthy",
                "timestamp", System.currentTimeMillis(),
                "service", "Gmail Webhook Controller"
            );
            
            return ResponseEntity.ok(health);
            
        } catch (Exception e) {
            logger.error("Health check failed", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("status", "unhealthy", "error", e.getMessage()));
        }
    }
    
    /**
     * Process Gmail push notification data
     * @param notificationData Base64 encoded notification data
     */
    private void processGmailNotification(String notificationData) {
        try {
            // Decode the notification data
            byte[] decodedData = java.util.Base64.getDecoder().decode(notificationData);
            String jsonData = new String(decodedData);
            
            logger.info("Processing Gmail notification data: {}", jsonData);
            
            // For now, we'll trigger a check for all monitored banks
            // In a more sophisticated implementation, you could parse the notification
            // to determine which specific emails triggered the notification
            
            List<BankConfigEntity> monitoredBanks = bankConfigRepository.findAll().stream()
                    .filter(bank -> gmailMonitoringService.isGmailMonitoringConfigured(bank))
                    .toList();
            
            for (BankConfigEntity bankConfig : monitoredBanks) {
                try {
                    processRecentEmailsForBank(bankConfig);
                } catch (Exception e) {
                    logger.error("Error processing emails for bank: {}", bankConfig.getBankName(), e);
                }
            }
            
        } catch (Exception e) {
            logger.error("Error processing Gmail notification data", e);
        }
    }
    
    /**
     * Process recent emails for a specific bank
     * @param bankConfig Bank configuration
     * @return Number of transactions processed
     */
    private int processRecentEmailsForBank(BankConfigEntity bankConfig) {
        logger.debug("Processing recent emails for bank: {}", bankConfig.getBankName());
        
        try {
            // Get recent emails (last 5 minutes worth)
            List<Message> messages = gmailMonitoringService.getRecentBankEmails(bankConfig, 5);
            
            if (messages.isEmpty()) {
                logger.debug("No new emails found for bank: {}", bankConfig.getBankName());
                return 0;
            }
            
            logger.info("Found {} potential transaction emails for bank: {}", messages.size(), bankConfig.getBankName());
            
            int processedCount = 0;
            
            for (Message message : messages) {
                try {
                    // Get full message details
                    Message fullMessage = gmailMonitoringService.getMessageDetails(message.getId());
                    if (fullMessage == null) {
                        continue;
                    }
                    
                    // Extract email content
                    String emailContent = gmailMonitoringService.extractTextContent(fullMessage);
                    if (emailContent == null || emailContent.trim().isEmpty()) {
                        continue;
                    }
                    
                    // Parse transaction from email
                    BankTransactionEntity transaction = parserService.parseTransactionEmail(
                            emailContent, message.getId(), bankConfig);
                    
                    if (transaction != null) {
                        // Record the transaction
                        BankTransactionEntity savedTransaction = transactionService.recordTransaction(transaction);
                        if (savedTransaction != null) {
                            processedCount++;
                            
                            // Send real-time WebSocket notification
                            webSocketService.sendNewTransactionNotification(savedTransaction);
                            
                            // Send balance change notification if balance is available
                            if (savedTransaction.getBalanceAfter() != null) {
                                webSocketService.sendBalanceChangeNotification(savedTransaction);
                            }
                            
                            logger.info("Successfully processed and notified transaction: {} for bank: {}", 
                                       savedTransaction.getTransactionId(), bankConfig.getBankName());
                        }
                    }
                    
                } catch (Exception e) {
                    logger.error("Error processing email message ID: {}", message.getId(), e);
                }
            }
            
            return processedCount;
            
        } catch (Exception e) {
            logger.error("Error processing recent emails for bank: {}", bankConfig.getBankName(), e);
            return 0;
        }
    }
}

package com.dkgold.legacy.gms.data;

import jakarta.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "bank_transaction")
public class BankTransactionEntity {
    
    public static final String STATUS_PROCESSED = "processed";
    public static final String STATUS_PENDING = "pending";
    public static final String STATUS_FAILED = "failed";
    
    public static final String TYPE_CREDIT = "credit";
    public static final String TYPE_DEBIT = "debit";
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;
    
    @Column(name = "transaction_id", unique = true, nullable = false)
    private String transactionId;
    
    @Column(name = "amount", nullable = false)
    private Long amount;
    
    @Column(name = "transaction_type", nullable = false)
    private String transactionType; // credit or debit
    
    @Column(name = "description", columnDefinition = "TEXT")
    private String description;
    
    @Column(name = "sender_account")
    private String senderAccount;
    
    @Column(name = "sender_name")
    private String senderName;
    
    @Column(name = "receiver_account")
    private String receiverAccount;
    
    @Column(name = "receiver_name")
    private String receiverName;
    
    @Column(name = "transaction_date", nullable = false)
    private LocalDateTime transactionDate;
    
    @Column(name = "balance_after")
    private Long balanceAfter;
    
    @Column(name = "status", nullable = false)
    private String status = STATUS_PENDING;
    
    @Column(name = "email_message_id")
    private String emailMessageId;
    
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt = LocalDateTime.now();
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    @ManyToOne
    @JoinColumn(name = "bank_config_id", nullable = false)
    private BankConfigEntity bankConfig;
    
    @ManyToOne
    @JoinColumn(name = "payment_tracking_code")
    private PaymentTrackingEntity paymentTracking;
    
    // Constructors
    public BankTransactionEntity() {}
    
    public BankTransactionEntity(String transactionId, Long amount, String transactionType, 
                                String description, LocalDateTime transactionDate, 
                                BankConfigEntity bankConfig) {
        this.transactionId = transactionId;
        this.amount = amount;
        this.transactionType = transactionType;
        this.description = description;
        this.transactionDate = transactionDate;
        this.bankConfig = bankConfig;
        this.createdAt = LocalDateTime.now();
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getTransactionId() {
        return transactionId;
    }
    
    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }
    
    public Long getAmount() {
        return amount;
    }
    
    public void setAmount(Long amount) {
        this.amount = amount;
    }
    
    public String getTransactionType() {
        return transactionType;
    }
    
    public void setTransactionType(String transactionType) {
        this.transactionType = transactionType;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public String getSenderAccount() {
        return senderAccount;
    }
    
    public void setSenderAccount(String senderAccount) {
        this.senderAccount = senderAccount;
    }
    
    public String getSenderName() {
        return senderName;
    }
    
    public void setSenderName(String senderName) {
        this.senderName = senderName;
    }
    
    public String getReceiverAccount() {
        return receiverAccount;
    }
    
    public void setReceiverAccount(String receiverAccount) {
        this.receiverAccount = receiverAccount;
    }
    
    public String getReceiverName() {
        return receiverName;
    }
    
    public void setReceiverName(String receiverName) {
        this.receiverName = receiverName;
    }
    
    public LocalDateTime getTransactionDate() {
        return transactionDate;
    }
    
    public void setTransactionDate(LocalDateTime transactionDate) {
        this.transactionDate = transactionDate;
    }
    
    public Long getBalanceAfter() {
        return balanceAfter;
    }
    
    public void setBalanceAfter(Long balanceAfter) {
        this.balanceAfter = balanceAfter;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    public String getEmailMessageId() {
        return emailMessageId;
    }
    
    public void setEmailMessageId(String emailMessageId) {
        this.emailMessageId = emailMessageId;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    public BankConfigEntity getBankConfig() {
        return bankConfig;
    }
    
    public void setBankConfig(BankConfigEntity bankConfig) {
        this.bankConfig = bankConfig;
    }
    
    public PaymentTrackingEntity getPaymentTracking() {
        return paymentTracking;
    }
    
    public void setPaymentTracking(PaymentTrackingEntity paymentTracking) {
        this.paymentTracking = paymentTracking;
    }
    
    @PreUpdate
    public void preUpdate() {
        this.updatedAt = LocalDateTime.now();
    }
}

package com.dkgold.legacy.gms.repository;

import com.dkgold.legacy.gms.data.BankTransactionEntity;
import com.dkgold.legacy.gms.data.BankConfigEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

public interface BankTransactionRepository extends JpaRepository<BankTransactionEntity, Long> {
    
    // Find transaction by transaction ID
    Optional<BankTransactionEntity> findByTransactionId(String transactionId);
    
    // Find transactions by bank config
    List<BankTransactionEntity> findByBankConfigOrderByTransactionDateDesc(BankConfigEntity bankConfig);
    
    // Find transactions by email message ID to avoid duplicates
    Optional<BankTransactionEntity> findByEmailMessageId(String emailMessageId);
    
    // Find transactions by status
    List<BankTransactionEntity> findByStatus(String status);
    
    // Find transactions by amount and description for matching with payment tracking
    @Query("SELECT bt FROM BankTransactionEntity bt WHERE bt.amount = :amount AND bt.description LIKE %:description% AND bt.status = 'pending'")
    List<BankTransactionEntity> findPendingTransactionsByAmountAndDescription(@Param("amount") Long amount, @Param("description") String description);
    
    // Find transactions within date range
    @Query("SELECT bt FROM BankTransactionEntity bt WHERE bt.transactionDate BETWEEN :startDate AND :endDate ORDER BY bt.transactionDate DESC")
    List<BankTransactionEntity> findTransactionsByDateRange(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);
    
    // Find credit transactions (incoming money)
    List<BankTransactionEntity> findByTransactionTypeAndBankConfigOrderByTransactionDateDesc(String transactionType, BankConfigEntity bankConfig);
    
    // Find unprocessed transactions for a specific bank account
    @Query("SELECT bt FROM BankTransactionEntity bt WHERE bt.bankConfig = :bankConfig AND bt.status = 'pending' ORDER BY bt.transactionDate DESC")
    List<BankTransactionEntity> findUnprocessedTransactionsByBankConfig(@Param("bankConfig") BankConfigEntity bankConfig);
    
    // Count transactions by status for monitoring
    @Query("SELECT COUNT(bt) FROM BankTransactionEntity bt WHERE bt.status = :status")
    long countByStatus(@Param("status") String status);
    
    // Find recent transactions for dashboard
    @Query("SELECT bt FROM BankTransactionEntity bt WHERE bt.createdAt >= :since ORDER BY bt.createdAt DESC")
    List<BankTransactionEntity> findRecentTransactions(@Param("since") LocalDateTime since);
}

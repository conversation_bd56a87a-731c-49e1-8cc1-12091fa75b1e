package com.dkgold.legacy.gms.repository;

import com.dkgold.legacy.gms.data.PaymentTrackingEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.Lock;
import org.springframework.data.repository.query.Param;
import jakarta.persistence.LockModeType;

public interface PaymentTrackingRepository extends JpaRepository<PaymentTrackingEntity, String> {
    
    // Find the tracking code with the highest sequence number for a given prefix
    @Query("SELECT MAX(p.trackingCode) FROM PaymentTrackingEntity p WHERE p.trackingCode LIKE :prefix%")
    String findMaxTrackingCodeByPrefix(@Param("prefix") String prefix);
    
    // Add a method to get next sequence with pessimistic locking
    @Lock(LockModeType.PESSIMISTIC_WRITE)
    @Query(value = "SELECT COALESCE(MAX(CAST(SUBSTRING(p.tracking_code, LENGTH(:prefix) + 1) AS UNSIGNED)), 0) + 1 " +
           "FROM payment_tracking p WHERE p.tracking_code LIKE :prefix%", 
           nativeQuery = true)
    int getNextSequenceNumber(@Param("prefix") String prefix);
}

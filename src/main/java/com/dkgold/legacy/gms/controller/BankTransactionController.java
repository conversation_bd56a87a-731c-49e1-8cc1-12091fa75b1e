package com.dkgold.legacy.gms.controller;

import com.dkgold.legacy.gms.data.BankConfigEntity;
import com.dkgold.legacy.gms.data.BankTransactionEntity;
import com.dkgold.legacy.gms.repository.BankConfigRepository;
import com.dkgold.legacy.gms.service.BankTransactionService;
import com.dkgold.legacy.gms.service.EmailMonitoringScheduler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("/api/bank-transactions")
public class BankTransactionController {
    
    private static final Logger logger = LoggerFactory.getLogger(BankTransactionController.class);
    
    @Autowired
    private BankTransactionService bankTransactionService;
    
    @Autowired
    private BankConfigRepository bankConfigRepository;
    
    @Autowired
    private EmailMonitoringScheduler emailMonitoringScheduler;
    
    /**
     * Get all transactions for a specific bank account
     */
    @GetMapping("/bank/{bankId}")
    public ResponseEntity<List<BankTransactionEntity>> getTransactionsByBank(@PathVariable Long bankId) {
        try {
            Optional<BankConfigEntity> bankConfig = bankConfigRepository.findById(bankId);
            if (bankConfig.isEmpty()) {
                return ResponseEntity.notFound().build();
            }
            
            List<BankTransactionEntity> transactions = bankTransactionService.getTransactionsByBankConfig(bankConfig.get());
            return ResponseEntity.ok(transactions);
            
        } catch (Exception e) {
            logger.error("Error getting transactions for bank ID: {}", bankId, e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * Get unprocessed transactions for a specific bank account
     */
    @GetMapping("/bank/{bankId}/unprocessed")
    public ResponseEntity<List<BankTransactionEntity>> getUnprocessedTransactionsByBank(@PathVariable Long bankId) {
        try {
            Optional<BankConfigEntity> bankConfig = bankConfigRepository.findById(bankId);
            if (bankConfig.isEmpty()) {
                return ResponseEntity.notFound().build();
            }
            
            List<BankTransactionEntity> transactions = bankTransactionService.getUnprocessedTransactions(bankConfig.get());
            return ResponseEntity.ok(transactions);
            
        } catch (Exception e) {
            logger.error("Error getting unprocessed transactions for bank ID: {}", bankId, e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * Get recent transactions across all banks
     */
    @GetMapping("/recent")
    public ResponseEntity<List<BankTransactionEntity>> getRecentTransactions(
            @RequestParam(defaultValue = "24") int hours) {
        try {
            List<BankTransactionEntity> transactions = bankTransactionService.getRecentTransactions(hours);
            return ResponseEntity.ok(transactions);
            
        } catch (Exception e) {
            logger.error("Error getting recent transactions", e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * Update transaction status
     */
    @PutMapping("/{transactionId}/status")
    public ResponseEntity<BankTransactionEntity> updateTransactionStatus(
            @PathVariable String transactionId,
            @RequestBody Map<String, String> statusUpdate) {
        try {
            String newStatus = statusUpdate.get("status");
            if (newStatus == null || newStatus.trim().isEmpty()) {
                return ResponseEntity.badRequest().build();
            }
            
            BankTransactionEntity updatedTransaction = bankTransactionService.updateTransactionStatus(transactionId, newStatus);
            return ResponseEntity.ok(updatedTransaction);
            
        } catch (RuntimeException e) {
            logger.error("Transaction not found: {}", transactionId);
            return ResponseEntity.notFound().build();
        } catch (Exception e) {
            logger.error("Error updating transaction status", e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * Manual transaction matching
     */
    @PostMapping("/{transactionId}/match/{paymentTrackingCode}")
    public ResponseEntity<Map<String, Object>> manualMatchTransaction(
            @PathVariable String transactionId,
            @PathVariable String paymentTrackingCode) {
        try {
            boolean success = bankTransactionService.manualMatchTransaction(transactionId, paymentTrackingCode);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", success);
            response.put("message", success ? "Transaction matched successfully" : "Failed to match transaction");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("Error in manual transaction matching", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Error occurred during matching");
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    /**
     * Get transaction statistics
     */
    @GetMapping("/statistics")
    public ResponseEntity<BankTransactionService.TransactionStatistics> getTransactionStatistics() {
        try {
            BankTransactionService.TransactionStatistics stats = bankTransactionService.getTransactionStatistics();
            return ResponseEntity.ok(stats);
            
        } catch (Exception e) {
            logger.error("Error getting transaction statistics", e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * Trigger manual email monitoring (for testing/admin purposes)
     */
    @PostMapping("/monitor/trigger")
    public ResponseEntity<Map<String, String>> triggerEmailMonitoring() {
        try {
            // Run email monitoring manually
            emailMonitoringScheduler.monitorBankEmails();
            
            Map<String, String> response = new HashMap<>();
            response.put("message", "Email monitoring triggered successfully");
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("Error triggering email monitoring", e);
            Map<String, String> response = new HashMap<>();
            response.put("error", "Failed to trigger email monitoring");
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    /**
     * Get monitoring status for all banks
     */
    @GetMapping("/monitoring/status")
    public ResponseEntity<List<Map<String, Object>>> getMonitoringStatus() {
        try {
            List<BankConfigEntity> allBanks = bankConfigRepository.findAll();
            
            List<Map<String, Object>> statusList = allBanks.stream().map(bank -> {
                Map<String, Object> status = new HashMap<>();
                status.put("bankId", bank.getId());
                status.put("bankName", bank.getBankName());
                status.put("accountNumber", bank.getAccountNumber());
                status.put("emailMonitoringEnabled", bank.getEmailMonitoringEnabled());
                status.put("monitoringEmail", bank.getMonitoringEmail());
                status.put("bankEmailSender", bank.getBankEmailSender());
                
                // Get recent transaction count
                List<BankTransactionEntity> recentTransactions = bankTransactionService.getRecentTransactions(24)
                        .stream()
                        .filter(t -> t.getBankConfig().getId().equals(bank.getId()))
                        .toList();
                status.put("recentTransactionCount", recentTransactions.size());
                
                return status;
            }).toList();
            
            return ResponseEntity.ok(statusList);
            
        } catch (Exception e) {
            logger.error("Error getting monitoring status", e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * Create manual bank transaction (for testing purposes)
     */
    @PostMapping("/manual")
    public ResponseEntity<BankTransactionEntity> createManualTransaction(@RequestBody CreateTransactionRequest request) {
        try {
            Optional<BankConfigEntity> bankConfig = bankConfigRepository.findById(request.getBankConfigId());
            if (bankConfig.isEmpty()) {
                return ResponseEntity.badRequest().build();
            }
            
            BankTransactionEntity transaction = new BankTransactionEntity();
            transaction.setTransactionId(request.getTransactionId());
            transaction.setAmount(request.getAmount());
            transaction.setTransactionType(request.getTransactionType());
            transaction.setDescription(request.getDescription());
            transaction.setSenderName(request.getSenderName());
            transaction.setSenderAccount(request.getSenderAccount());
            transaction.setBankConfig(bankConfig.get());
            transaction.setTransactionDate(java.time.LocalDateTime.now());
            
            BankTransactionEntity savedTransaction = bankTransactionService.recordTransaction(transaction);
            return ResponseEntity.ok(savedTransaction);
            
        } catch (Exception e) {
            logger.error("Error creating manual transaction", e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * Request DTO for manual transaction creation
     */
    public static class CreateTransactionRequest {
        private String transactionId;
        private Long amount;
        private String transactionType;
        private String description;
        private String senderName;
        private String senderAccount;
        private Long bankConfigId;
        
        // Getters and setters
        public String getTransactionId() { return transactionId; }
        public void setTransactionId(String transactionId) { this.transactionId = transactionId; }
        
        public Long getAmount() { return amount; }
        public void setAmount(Long amount) { this.amount = amount; }
        
        public String getTransactionType() { return transactionType; }
        public void setTransactionType(String transactionType) { this.transactionType = transactionType; }
        
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
        
        public String getSenderName() { return senderName; }
        public void setSenderName(String senderName) { this.senderName = senderName; }
        
        public String getSenderAccount() { return senderAccount; }
        public void setSenderAccount(String senderAccount) { this.senderAccount = senderAccount; }
        
        public Long getBankConfigId() { return bankConfigId; }
        public void setBankConfigId(Long bankConfigId) { this.bankConfigId = bankConfigId; }
    }
}

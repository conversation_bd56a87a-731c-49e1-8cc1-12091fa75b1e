<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bank Transaction Notifications - Real-time Test</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/sockjs-client/1.5.2/sockjs.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/stomp.js/2.3.3/stomp.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .connected { background-color: #d4edda; color: #155724; }
        .disconnected { background-color: #f8d7da; color: #721c24; }
        .notification {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .notification.new-transaction { border-left-color: #4caf50; background: #e8f5e8; }
        .notification.balance-change { border-left-color: #ff9800; background: #fff3e0; }
        .notification.payment-matched { border-left-color: #9c27b0; background: #f3e5f5; }
        .notification.transaction-failed { border-left-color: #f44336; background: #ffebee; }
        .controls {
            margin: 20px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 4px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            text-align: center;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #007bff;
        }
        #notifications {
            max-height: 500px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏦 Bank Transaction Notifications - Real-time Test</h1>
        
        <div id="connection-status" class="status disconnected">
            ❌ Disconnected from WebSocket
        </div>
        
        <div class="controls">
            <button id="connect-btn" onclick="connect()">Connect to WebSocket</button>
            <button id="disconnect-btn" onclick="disconnect()" disabled>Disconnect</button>
            <button onclick="clearNotifications()">Clear Notifications</button>
            <button onclick="triggerManualCheck()">Trigger Manual Gmail Check</button>
        </div>
        
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number" id="total-notifications">0</div>
                <div>Total Notifications</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="new-transactions">0</div>
                <div>New Transactions</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="balance-changes">0</div>
                <div>Balance Changes</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="payment-matches">0</div>
                <div>Payment Matches</div>
            </div>
        </div>
        
        <h3>📨 Real-time Notifications</h3>
        <div id="notifications">
            <p style="color: #666; text-align: center;">No notifications yet. Connect to WebSocket to start receiving real-time updates.</p>
        </div>
    </div>

    <script>
        let stompClient = null;
        let stats = {
            total: 0,
            newTransactions: 0,
            balanceChanges: 0,
            paymentMatches: 0
        };

        function connect() {
            const socket = new SockJS('/ws/bank-notifications');
            stompClient = Stomp.over(socket);
            
            stompClient.connect({}, function (frame) {
                console.log('Connected: ' + frame);
                updateConnectionStatus(true);
                
                // Subscribe to all bank notification topics
                stompClient.subscribe('/topic/bank/notifications/all', function (message) {
                    const notification = JSON.parse(message.body);
                    displayNotification(notification);
                });
                
                stompClient.subscribe('/topic/bank/transactions/new', function (message) {
                    const notification = JSON.parse(message.body);
                    console.log('New transaction:', notification);
                });
                
                stompClient.subscribe('/topic/bank/balance/changes', function (message) {
                    const notification = JSON.parse(message.body);
                    console.log('Balance change:', notification);
                });
                
                stompClient.subscribe('/topic/bank/payments/matched', function (message) {
                    const notification = JSON.parse(message.body);
                    console.log('Payment matched:', notification);
                });
                
                stompClient.subscribe('/topic/bank/system/status', function (message) {
                    const notification = JSON.parse(message.body);
                    console.log('System status:', notification);
                });
                
            }, function (error) {
                console.error('WebSocket connection error:', error);
                updateConnectionStatus(false);
                setTimeout(connect, 5000); // Retry after 5 seconds
            });
        }

        function disconnect() {
            if (stompClient !== null) {
                stompClient.disconnect();
            }
            updateConnectionStatus(false);
            console.log("Disconnected");
        }

        function updateConnectionStatus(connected) {
            const statusDiv = document.getElementById('connection-status');
            const connectBtn = document.getElementById('connect-btn');
            const disconnectBtn = document.getElementById('disconnect-btn');
            
            if (connected) {
                statusDiv.className = 'status connected';
                statusDiv.innerHTML = '✅ Connected to WebSocket - Listening for real-time notifications';
                connectBtn.disabled = true;
                disconnectBtn.disabled = false;
            } else {
                statusDiv.className = 'status disconnected';
                statusDiv.innerHTML = '❌ Disconnected from WebSocket';
                connectBtn.disabled = false;
                disconnectBtn.disabled = true;
            }
        }

        function displayNotification(notification) {
            const notificationsDiv = document.getElementById('notifications');
            
            // Clear the "no notifications" message
            if (stats.total === 0) {
                notificationsDiv.innerHTML = '';
            }
            
            const notificationDiv = document.createElement('div');
            notificationDiv.className = `notification ${notification.type.toLowerCase().replace('_', '-')}`;
            
            const typeEmoji = {
                'NEW_TRANSACTION': '💰',
                'BALANCE_CHANGE': '📊',
                'PAYMENT_MATCHED': '✅',
                'TRANSACTION_FAILED': '❌'
            };
            
            notificationDiv.innerHTML = `
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <div>
                        <strong>${typeEmoji[notification.type] || '📨'} ${notification.type.replace('_', ' ')}</strong>
                        <div style="margin: 5px 0;">
                            <strong>Bank:</strong> ${notification.bankName || 'N/A'} | 
                            <strong>Amount:</strong> ${formatCurrency(notification.amount)} | 
                            <strong>Type:</strong> ${notification.transactionType || 'N/A'}
                        </div>
                        ${notification.description ? `<div><strong>Description:</strong> ${notification.description}</div>` : ''}
                        ${notification.senderName ? `<div><strong>Sender:</strong> ${notification.senderName}</div>` : ''}
                        ${notification.balanceAfter ? `<div><strong>Balance After:</strong> ${formatCurrency(notification.balanceAfter)}</div>` : ''}
                    </div>
                    <div style="text-align: right; font-size: 0.9em; color: #666;">
                        <div>${notification.transactionId || 'N/A'}</div>
                        <div>${new Date(notification.notificationTime).toLocaleString()}</div>
                    </div>
                </div>
            `;
            
            notificationsDiv.insertBefore(notificationDiv, notificationsDiv.firstChild);
            
            // Update statistics
            stats.total++;
            if (notification.type === 'NEW_TRANSACTION') stats.newTransactions++;
            if (notification.type === 'BALANCE_CHANGE') stats.balanceChanges++;
            if (notification.type === 'PAYMENT_MATCHED') stats.paymentMatches++;
            
            updateStats();
            
            // Keep only last 50 notifications
            while (notificationsDiv.children.length > 50) {
                notificationsDiv.removeChild(notificationsDiv.lastChild);
            }
        }

        function updateStats() {
            document.getElementById('total-notifications').textContent = stats.total;
            document.getElementById('new-transactions').textContent = stats.newTransactions;
            document.getElementById('balance-changes').textContent = stats.balanceChanges;
            document.getElementById('payment-matches').textContent = stats.paymentMatches;
        }

        function clearNotifications() {
            document.getElementById('notifications').innerHTML = 
                '<p style="color: #666; text-align: center;">Notifications cleared.</p>';
            stats = { total: 0, newTransactions: 0, balanceChanges: 0, paymentMatches: 0 };
            updateStats();
        }

        function triggerManualCheck() {
            fetch('/api/gmail/webhook/trigger-check', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.text())
            .then(data => {
                alert('Manual check triggered: ' + data);
            })
            .catch(error => {
                console.error('Error triggering manual check:', error);
                alert('Error triggering manual check');
            });
        }

        function formatCurrency(amount) {
            if (!amount) return 'N/A';
            return new Intl.NumberFormat('vi-VN', {
                style: 'currency',
                currency: 'VND'
            }).format(amount);
        }

        // Auto-connect on page load
        window.onload = function() {
            connect();
        };

        // Reconnect on page focus (in case connection was lost)
        window.onfocus = function() {
            if (stompClient === null || !stompClient.connected) {
                connect();
            }
        };
    </script>
</body>
</html>

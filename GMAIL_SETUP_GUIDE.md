# Gmail API Setup Guide for Bank Transaction Monitoring

This guide explains how to set up Gmail API access for real-time bank transaction monitoring.

## Prerequisites

1. Google Cloud Platform account
2. Gmail account that receives bank transaction notifications
3. Java application with Gmail API dependencies

## Step 1: Create Google Cloud Project

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Note down your project ID

## Step 2: Enable Gmail API

1. In Google Cloud Console, go to "APIs & Services" > "Library"
2. Search for "Gmail API"
3. Click on Gmail API and click "Enable"

## Step 3: Create Credentials

1. Go to "APIs & Services" > "Credentials"
2. Click "Create Credentials" > "OAuth client ID"
3. If prompted, configure the OAuth consent screen:
   - Choose "External" user type
   - Fill in required fields (App name, User support email, Developer contact)
   - Add your email to test users
4. For Application type, choose "Desktop application"
5. Give it a name (e.g., "GoldApp Bank Monitor")
6. Click "Create"

## Step 4: Download Credentials

1. After creating the OAuth client, click the download button
2. Save the file as `credentials.json`
3. Place this file in your application's `src/main/resources/` directory

## Step 5: Configure Application Properties

Add the following to your `application.properties`:

```properties
# Gmail API Configuration
app.gmail.credentials-path=credentials.json
app.gmail.tokens-directory=tokens

# Email Configuration for notifications
spring.mail.host=smtp.gmail.com
spring.mail.port=587
spring.mail.username=<EMAIL>
spring.mail.password=your-app-password
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true

# Admin notification email
app.notification.admin-email=<EMAIL>
```

## Step 6: Configure Bank Account Email Monitoring

Update your bank configuration in the database:

```sql
UPDATE bank_config SET 
    monitoring_email = '<EMAIL>',
    email_monitoring_enabled = 1,
    bank_email_sender = '<EMAIL>',
    transaction_email_subject_pattern = 'Transaction notification'
WHERE id = 1;
```

## Step 7: First Run Authentication

1. Start your application
2. The first time the Gmail API is accessed, it will open a browser window
3. Sign in with the Gmail account that receives bank notifications
4. Grant the requested permissions
5. The application will save authentication tokens for future use

## Step 8: Test the Setup

1. Use the API endpoint to trigger manual monitoring:
   ```
   POST /api/bank-transactions/monitor/trigger
   ```

2. Check the monitoring status:
   ```
   GET /api/bank-transactions/monitoring/status
   ```

## Bank Email Patterns

Different banks have different email formats. Configure the patterns accordingly:

### Vietcombank
- Sender: `<EMAIL>`
- Subject pattern: `Thong bao bien dong so du`

### Techcombank
- Sender: `<EMAIL>`
- Subject pattern: `Thong bao giao dich`

### BIDV
- Sender: `<EMAIL>`
- Subject pattern: `Thong bao tai khoan`

## Security Considerations

1. **Credentials Security**: Keep `credentials.json` secure and never commit it to version control
2. **Token Storage**: The `tokens` directory contains sensitive authentication data
3. **Email Access**: The application only requests read-only access to Gmail
4. **Network Security**: Use HTTPS in production
5. **Database Security**: Encrypt sensitive data in the database

## Troubleshooting

### Common Issues

1. **"The file credentials.json is not found"**
   - Ensure credentials.json is in src/main/resources/
   - Check the file path configuration

2. **"Access blocked: This app's request is invalid"**
   - Verify OAuth consent screen is configured
   - Add your email to test users
   - Check redirect URIs

3. **"Gmail API has not been used in project"**
   - Enable Gmail API in Google Cloud Console
   - Wait a few minutes for activation

4. **"Token has been expired or revoked"**
   - Delete the tokens directory
   - Re-authenticate on next run

### Monitoring Logs

Check application logs for monitoring activity:

```bash
# Enable debug logging for Gmail monitoring
logging.level.com.dkgold.legacy.gms.service.GmailMonitoringService=DEBUG
logging.level.com.dkgold.legacy.gms.service.EmailMonitoringScheduler=DEBUG
```

## Production Deployment

1. **OAuth Consent Screen**: Submit for verification if needed
2. **Service Account**: Consider using service account for production
3. **Rate Limits**: Monitor Gmail API quotas and limits
4. **Error Handling**: Implement robust error handling and retry logic
5. **Monitoring**: Set up application monitoring and alerting

## API Endpoints

- `GET /api/bank-transactions/recent` - Get recent transactions
- `GET /api/bank-transactions/bank/{bankId}` - Get transactions for specific bank
- `POST /api/bank-transactions/monitor/trigger` - Trigger manual monitoring
- `GET /api/bank-transactions/monitoring/status` - Check monitoring status
- `GET /api/bank-transactions/statistics` - Get transaction statistics

## Support

For issues with this setup:
1. Check application logs
2. Verify Gmail API quotas in Google Cloud Console
3. Test with a simple Gmail API call
4. Contact system administrator
